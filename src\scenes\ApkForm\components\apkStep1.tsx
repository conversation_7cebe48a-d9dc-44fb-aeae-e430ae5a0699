
import { Text, FontWeights, IButtonStyles, IChoiceGroupOption, IconButton, IDropdownOption, IIconProps, mergeStyleSets, Modal, Spinner, SpinnerSize, Stack, IComboBoxOption, Selection, SelectionMode, PrimaryButton, DefaultButton } from "@fluentui/react";
import React from "react";
import { L } from "../../../lib/abpUtility";
import AppConsts from "../../../lib/appconst";
import clientService from "../../../services/client/clientService";
import { CalculationSectionType } from "../../../services/dto/calculationSectionTypeEnums";
import { ProductDto } from "../../../services/product/productDto";
import {additionalTheme, myTheme} from "../../../styles/theme";
import { getDropdownOptionsFromDataSource, getInputIconData, getInputTableData, getInputValidationData, isCountriesInput, validateNip, validatePesel, validateRegon } from "../../../utils/inputUtils";
import { getLocaleName } from "../../../utils/languageUtils";
import { conditionalAttribute, mapAttributeKeyToId, renderElement } from "../../../utils/policyCalculationUtils";
import { generateDropdownOptionsIfCountriesInput } from "../../../utils/storeUtils";
import { catchErrorMessage, filterBySome, isJsonString } from "../../../utils/utils";
import { DropdownBase } from "../../BaseComponents";
import { ContentViewModelProperty } from "../../BaseComponents/contentViewBase";
import { Controls } from "../../BaseComponents/controls";
import { ClientDto } from "../../../services/client/dto/clientDto";
import { defaultClient } from "../../../stores/clientStore";
import { defaultUser } from "../../../stores/userCrudStore";
import { CustomerFluentListBaseWithCommandBar } from "../../BaseComponents/customerFluentListBaseWithCommandBar";

var hash = require('object-hash');
var _ = require('lodash');

const cancelIcon: IIconProps = { iconName: 'Cancel' };

const iconButtonStyles: Partial<IButtonStyles> = {
    root: {
        color: myTheme.palette.neutralPrimary,
        marginLeft: 'auto',
        marginTop: '4px',
        marginRight: '2px',
    },
    rootHovered: {
        color: myTheme.palette.neutralDark,
    },
};

const classNames = mergeStyleSets({
    fontBold: {
        fontWeight: '800',
    },
    sectionContainerHeader: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%',
        marginBottom: '20px'
    },
    sectionContainterTitle: {
        fontSize: '20px',
        color: additionalTheme.grey,
    },
    sectionContainter: {
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        width: 'fit-content',
        marginTop: '30px !important',
        minWidth: '30%',
        maxWidth: '1200px',
        minHeight: '150px',
        padding: '35px',
        boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.25)',
        borderRadius: '12px',
        selectors: {
            ':first-child': {
                marginTop: '25px',
            }
        }
    },
    sectionContainterHidden: {
        minHeight: 'auto',
        selectors: {
            '& p': {
                marginTop: '15px',
                wordBreak: 'break-all',
                selectors: {
                    '& span': {
                        fontWeight: 'bold',
                    }
                }
            }
        }
    },
    container: {
        display: 'flex',
        flexFlow: 'column nowrap',
        alignItems: 'stretch',
        justifyContent: 'center',
        width: '75%',
        overflowX: 'hidden',
    },
    header: [
        myTheme.fonts.large,
        {
            backgroundColor: myTheme.palette.white,
            flex: '1 1 auto',
            color: myTheme.palette.neutralPrimary,
            display: 'flex',
            alignItems: 'center',
            fontWeight: FontWeights.semibold,
            padding: '12px 12px 14px 24px',
        },
    ],
    contentContainer: {
        width: '100%',
        height: '80vh',
        padding: '0 20px 40px 20px',
        boxSizing: 'border-box',
        backgroundColor: myTheme.palette.white,
        selectors: {
            '> div': {
                marginRight: '0',
                width: '99.9%',
                maxWidth: '99.9%',
                backgroundColor: myTheme.palette.white,
                selectors: {
                    '> div': {
                        width: '99.9%',
                        maxWidth: '99.9%',
                    }
                }
            }
        }
    },
    modalActionButton: {
        display: 'block',
        margin: '30px auto 0',
    },
    sectionIcon: {
        // margin: '0 10px',
        selectors: {
            ':not(:first-child)': {
                marginLeft: '10px',
            },
            '& .ms-Icon': {
                fontSize: '28px',
            }
        }
    },
    loadSpinner: {
        display: 'inline-flex',
        marginLeft: '25px',
        marginTop: '35px',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    smallLoadSpinner: {
        display: 'inline-flex',
        marginLeft: 'auto',
        marginRight: '15px',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    customCheckbox: {
        marginTop: '25px !important',
    },
    callout: {
        width: 320,
        padding: '20px 24px',
        background: myTheme.semanticColors.bodyBackground,
    },
    title: {
        marginBottom: 12,
        fontWeight: FontWeights.semilight,
    },
    actions: {
        marginTop: 20,
    },
    messageBar: {
        width: 'fit-content'
    },
    messageBarMargin: {
        margin: '5px auto 5px 0'
    },
});

export interface IApkStep1Props {
    productStore: any;
    allProducts: ProductDto[];
    selectedProduct: string | number | undefined;
    clientStore: any;
    selectedClient: string;
    selectedClientData: ClientDto | undefined;
    tempSelectedClient: string;
    asyncActionInProgress: boolean | undefined;
    productAttributes: any;
    product: any;
    isDataLoaded: boolean;
    inputsTypeValuePairs: any;
    inputsIdUserFieldsPairs: any;
    gnLanguage: any;
    countryStore: any;
    autoCalculationOwner: string;
    inputErrors: number;
    customerTypeValuePairs: any;
    blockNextStepButton: boolean;
    allUserFields: any[];
    history: any;
    loadSpinnerCustomLabel?: string | null;
    setInputErrors: (errorsCount: number) => void;
    onAutoCalculationOwnerChange: (value: string) => void;
    onProductSelect: (productId: string | number | undefined) => void;
    onCustomerSelect: (customer: any, justId: boolean) => void;
    onTempCustomerSelect: (tempCustomer: any, justId: boolean) => void;
    onInputChange: (id: string, value: any, userFields: any) => void;
    onFillFormWithSelectedClientData: (productAttributeMappingsToChange: any[]) => void;
    onCloneProductAttributeMappingsData: (productAttributeMappingsToChange: any[], productAttributeMappingsToClone: any[]) => void;
    toggleAsyncActionInProgress: (bool: boolean) => void;
    setBlockNextStepButton: (value: boolean, message?: string) => void;
    onMassInputChange: (inputFields: any, userFields: any) => void;
    mapKeyToId: (mapType: string, key: string) => string;
}

export class ApkStep1 extends React.Component<IApkStep1Props> {
    private defaultDataSet: boolean = false;
    private filteredProductAttributes: any[] = [];
    private filteredProductAttributeMappings: any[] = [];
    private inputsToSet: any[] = [];
    private exampleDataLoadedFlag: boolean = false;
    private tempInputIdUserFieldPairs: any = {};
    private isModalOpen: boolean = false;
    private activeSection: string = "";
    private prevTempInputErrors: any = {
        [CalculationSectionType.Insurer]: 0 as number,
        [CalculationSectionType.Owner]: 0 as number,
        [CalculationSectionType.CoOwner]: 0 as number,
        [CalculationSectionType.User]: 0 as number,
    };
    private tempInputErrors: any = {
        [CalculationSectionType.Insurer]: 0 as number,
        [CalculationSectionType.Owner]: 0 as number,
        [CalculationSectionType.CoOwner]: 0 as number,
        [CalculationSectionType.User]: 0 as number,
    };
    private calloutText: string = "";
    private prevProductId: string = "";
    private isCalloutVisible: any = {
        [CalculationSectionType.Insurer]: false as boolean,
        [CalculationSectionType.Owner]: false as boolean,
        [CalculationSectionType.CoOwner]: false as boolean,
        [CalculationSectionType.User]: false as boolean,
    };
    private prevCustomerTypeValuePairsHash: any = {
        [CalculationSectionType.Insurer]: "" as string,
        [CalculationSectionType.Owner]: "" as string,
        [CalculationSectionType.CoOwner]: "" as string,
        [CalculationSectionType.User]: "" as string,
    };
    private forceInsurerCustomerSave: any = {
        [CalculationSectionType.Insurer]: false as boolean,
        [CalculationSectionType.Owner]: false as boolean,
        [CalculationSectionType.CoOwner]: false as boolean,
        [CalculationSectionType.User]: false as boolean,
    };
    private customerDataPreFilled: boolean = false;
    private hideInputs: any = {
        [CalculationSectionType.Insurer]: false as boolean,
        [CalculationSectionType.Owner]: false as boolean,
        [CalculationSectionType.CoOwner]: false as boolean,
        [CalculationSectionType.User]: false as boolean,
    };
    private blockToggleInsurerDetailsIcon: boolean = false;
    private preselectedClient: any;
    private prevTempSelectedClient: string = "";
    private prevSelectedClient: string = "";
    private selectedClientContext: any = {
        [CalculationSectionType.Insurer]: { id: 0 as number, customerId: '' as string, client: {} as any },
        [CalculationSectionType.Owner]: { id: 0 as number, customerId: '' as string, client: {} as any },
        [CalculationSectionType.CoOwner]: { id: 0 as number, customerId: '' as string, client: {} as any },
        [CalculationSectionType.User]: { id: 0 as number, customerId: '' as string, client: {} as any },
    };
    private productOptions: IDropdownOption[] = [];
    private insurerAttributes: any[] = [];
    private lastChangedSection: string = CalculationSectionType.Insurer;
    private throttledSetLastChangedSection: any = _.throttle((newLastChangedSection: string, value?: string) => {
        if (this.lastChangedSection.length === 0 || this.lastChangedSection !== newLastChangedSection) {
            this.lastChangedSection = newLastChangedSection;
        }
    }, 10, []);
    private componentMountTimestamp: any = null;
    private whoIsOwnerChangeTimestamp: any = null;
    private selectedClientFullname: string = "";
    private prevSelectedClientDataId: string = '';
    private _clientListSelection: Selection = new Selection({
        onSelectionChanged: () => {
            const selectedClient: any = this._clientListSelection.getSelection();
            if (Array.isArray(selectedClient) && selectedClient.length > 0 && !!selectedClient[0].id) {
                this.selectedClientContext[this.activeSection] = {
                    id: parseInt(this.selectedClientContext[this.activeSection].id),
                    customerId: selectedClient[0].customerId,
                    client: selectedClient[0],
                };
                this.props.onTempCustomerSelect(selectedClient[0], false);

                this.selectedClientFullname = !!selectedClient[0].fullName ? selectedClient[0].fullName : selectedClient[0].user.fullName;
                this._clientListSelection.setAllSelected(false);
            }
            this.forceUpdate();
        },
        selectionMode: SelectionMode.single
    });

    // private debouncedOnInputChange: any = _.debounce((inputKey: string, value: any, userFields: any) => {
    //     this.props.onInputChange(inputKey, value, userFields);
    // }, AppConsts.defaultInputsDelay, []);

    componentDidUpdate() {
        // Force re-render when selectedClientData changes to update form inputs with defaultValue
        const currentSelectedClientDataId = this.props.selectedClientData?.id || '';
        if(this.prevSelectedClientDataId !== currentSelectedClientDataId) {
            this.prevSelectedClientDataId = currentSelectedClientDataId;

            this.insurerAttributes = [];
            
            this.forceUpdate();
        }

        if (!this.defaultDataSet && this.inputsToSet.length > 0) {
            this.setDefaultInputsData();

            this.defaultDataSet = true;
            this.forceUpdate();
        }
    }

    private clearAllData(section: string) {
        const { inputsTypeValuePairs, inputsIdUserFieldsPairs } = this.props;
        let sectionProductAttributeMappings = this.filteredProductAttributeMappings.filter((x) => x.section === section);

        sectionProductAttributeMappings.forEach((ProductAttributeMapping: any) => {
            if (ProductAttributeMapping && ProductAttributeMapping.ProductAttribute && ProductAttributeMapping.ProductAttribute.UserFields
                && ["TextBox", "Datepicker", "MultilineTextbox", "ColorSquares"].includes(ProductAttributeMapping.AttributeControlTypeId)) {
                let keyUserField: any = filterBySome(ProductAttributeMapping.ProductAttribute.UserFields, "Key", "key");
                if (keyUserField && !!keyUserField.Value) {
                    let splittedKeyUserFieldValue = keyUserField.Value.split('.');

                    if (splittedKeyUserFieldValue[splittedKeyUserFieldValue.length - 1] !== 'Nationality') {
                        const foundInputId: string = mapAttributeKeyToId(sectionProductAttributeMappings, keyUserField.Value, true);
                        if (!!inputsTypeValuePairs[foundInputId]) {
                            this.props.onInputChange(foundInputId, undefined, inputsIdUserFieldsPairs[foundInputId]);
                        }
                    }
                }
            }
        });

        if (section === CalculationSectionType.Insurer) {
            this.props.onTempCustomerSelect("", true);
            this.props.onCustomerSelect("", true);
        }

        this.selectedClientContext[section] = {
            id: 0,
            customerId: '',
            client: {},
        };

        this.lastChangedSection = section;

        this.forceInsurerCustomerSave[section] = false;
        this.prevCustomerTypeValuePairsHash[section] = "";
    }

    private setDefaultInputsData() {
        if (this.inputsToSet.length > 0) {
            const cloneInputsTypeValuePairs = {};
            const cloneInputsIdUserFieldsPairs = {};

            this.inputsToSet.forEach((element) => {
                if (!this.props.inputsTypeValuePairs[element.id] || this.props.inputsTypeValuePairs[element.id].length === 0) {
                    cloneInputsTypeValuePairs[element.id] = element.value ?
                        (isJsonString(element.value) ?
                            JSON.parse(element.value) : element.value)
                        : element.value;
                    cloneInputsIdUserFieldsPairs[element.id] = this.tempInputIdUserFieldPairs[element.id];
                }
            });

            this.props.onMassInputChange(cloneInputsTypeValuePairs, cloneInputsIdUserFieldsPairs);
            this.inputsToSet = [];
        }
    }

    private async saveCustomer(section: string) {
        const { inputsTypeValuePairs } = this.props;
        this.lastChangedSection = section;
        let sectionProductAttributeMappings = this.filteredProductAttributeMappings.filter((x) => x.section === section);

        if (!!inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "Pesel")] || !!inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "Nip")]) {
            this.props.toggleAsyncActionInProgress(true);

            let filteredProductAttributeMapping: any = filterBySome(sectionProductAttributeMappings, "Id", mapAttributeKeyToId(sectionProductAttributeMappings, "ClientType"));
            let clientTypeOptionId = mapAttributeKeyToId(sectionProductAttributeMappings, "ClientType");
            let clientTypeName = inputsTypeValuePairs[clientTypeOptionId];
            for (let key in filteredProductAttributeMapping.ProductAttributeValues) {
                if (filteredProductAttributeMapping.ProductAttributeValues.hasOwnProperty(key) &&
                    filteredProductAttributeMapping.ProductAttributeValues[key].Id === inputsTypeValuePairs[clientTypeOptionId]) {
                    clientTypeName = filteredProductAttributeMapping.ProductAttributeValues[key].Name;
                }
            }

            let inputObj: ClientDto = {
                ...defaultClient,
                // "email": inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "Email")],
                // !!inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "Email")] ?
                // inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "Email")] : 
                //     `top_${inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "Pesel")]}@a-soft.pl`,
                // "firstName": inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "FirstName")],
                // "lastName": inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "Surname")],
                "streetAddress": inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "Street")],
                "streetAddress2": inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "HouseNumber")],
                "zipPostalCode": inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "PostCode")],
                "city": inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "City")],
                "countryId": inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "Name")],
                "stateProvinceId": inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "County")],
                "county": inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "County")],
                "phone": inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "MobilePhone")],
                "pesel": inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "Pesel")],
                "clientType": clientTypeName,
                "nip": inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "Nip")],
                "regon": inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "Regon")],
                "company": inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "CompanyName")],
                "nationality": inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "Nationality")],
                "note": inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "Note")],
                "emailAdditional": inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "AdditionalEmail")],
                "user": {
                    ...defaultUser,
                    "name": inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "FirstName")],
                    "surname": inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "Surname")],
                    "emailAddress": inputsTypeValuePairs[mapAttributeKeyToId(sectionProductAttributeMappings, "Email")],
                },
            };

            if (!!this.selectedClientContext[section].id && !!this.selectedClientContext[section].customerId) {
                inputObj["customerId"] = this.selectedClientContext[section].customerId;
                inputObj["id"] = this.selectedClientContext[section].id;
            } else {
                inputObj["id"] = '0';
            }

            await clientService.createOrUpdate(inputObj).then((response: any) => {
                if (response.success && response.error === null) {
                    this.calloutText = L("Customer data has been successfully saved!");
                    this.forceInsurerCustomerSave[section] = false;
                    this.blockToggleInsurerDetailsIcon = true;
                    //this.props.onCustomerSelect(response.customer); TODO ####
                } else {
                    this.calloutText = L(Array.isArray(response.error) ? response.error[0] : response.error);
                }
                this.isCalloutVisible[section] = true;
                this.props.toggleAsyncActionInProgress(false);
            }).catch((error: any) => {
                this.calloutText = catchErrorMessage(error);
                this.isCalloutVisible[section] = true;
                this.props.toggleAsyncActionInProgress(false);
            });
        } else {
            this.calloutText = L('Incorrect data.');
            this.isCalloutVisible[section] = true;
            this.forceUpdate();
        }
    }

    private filterProductAttributes() {
        let {product} = this.props;

        this.filteredProductAttributes = [];

        if(product && product.ProductAttributeMappings && product.ProductAttributeMappings.length > 0) {
            product.ProductAttributeMappings.forEach((productAttribute: any) => {
                if(productAttribute.ProductAttribute && productAttribute.ProductAttribute && productAttribute.ProductAttribute.UserFields) {
                    let userFieldsFound: number = 0;
                    let stepSection: number = 0;
                    let section: string = "";

                    productAttribute.ProductAttribute.UserFields.some((UserField: any) => {
                        if(!!UserField) {
                            if(UserField.Key === "section") {
                                section = UserField.Value;
                                userFieldsFound++;
                            }
    
                            if(UserField.Key === "step_section") {
                                stepSection = Number(UserField.Value);
                                userFieldsFound++;
                            }
                            
                            if(userFieldsFound === 2) {
                                productAttribute['id'] = productAttribute.Id;
                                productAttribute['inputOrderNumber'] = stepSection;
                                productAttribute['section'] = section;
                                this.filteredProductAttributes.push(productAttribute);
                                return true;
                            }
                        }
                        return false;
                    });
                }
            });

            this.filteredProductAttributes.sort((a,b) => (a.inputOrderNumber > b.inputOrderNumber) ? 1 : ((b.inputOrderNumber > a.inputOrderNumber) ? -1 : 0));
        }
    }

    private getFilteredProductAttributeMappingBySection(section: string): any {
        let productAttributesToReturn: any[] = [];
        
        this.filteredProductAttributeMappings.forEach((productAttributeMapping: any) => {
            if(productAttributeMapping && productAttributeMapping.section && productAttributeMapping.section === section) {
                productAttributesToReturn.push(productAttributeMapping);
            }
        });
        
        productAttributesToReturn.sort((a,b) => (a.inputOrderNumber > b.inputOrderNumber) ? 1 : ((b.inputOrderNumber > a.inputOrderNumber) ? -1 : 0));

        return productAttributesToReturn;
    }

    private filterProductMappings() {
        const {product} = this.props;

        this.filteredProductAttributeMappings = [];

        if(product && product.ProductAttributeMappings && product.ProductAttributeMappings.length > 0) {
            let filteredProductAttributeIds: string[] = [];
            let filteredProductAttributes: any = {};

            this.filteredProductAttributes.forEach((attr: any) => {
                filteredProductAttributeIds.push(attr.Id);
                filteredProductAttributes[attr.Id] = attr;
            });

            product.ProductAttributeMappings.forEach((attributeMapping: any) => {
                this.filteredProductAttributeMappings.push(attributeMapping);
            });

            this.filteredProductAttributeMappings.sort((a,b) => (a.inputOrderNumber > b.inputOrderNumber) ? 1 : ((b.inputOrderNumber > a.inputOrderNumber) ? -1 : 0));
        }
    }

    private getAttrKey(UserFields: any[]): string {
        let keyToReturn: string = "";

        if (UserFields && UserFields.length > 0) {
            UserFields.some((UserField: any) => {
                if (UserField.Key === "key") {
                    keyToReturn = UserField.Value;
                    return true;
                }
                return false;
            });
        }

        return keyToReturn;
    }

    private clearAllTempErrors() {
        this.tempInputErrors[CalculationSectionType.Insurer] = 0;
    }

    private clearSectionTempErrors(section: string) {
        this.tempInputErrors[section] = 0;
    }

    private getSumOfAllTempErrors(): number {
        return this.tempInputErrors[CalculationSectionType.Insurer] +
            this.tempInputErrors[CalculationSectionType.Owner] +
            this.tempInputErrors[CalculationSectionType.CoOwner] +
            this.tempInputErrors[CalculationSectionType.User];
    }

    private isAnySectionForcedToSave(): boolean {
        if (this.forceInsurerCustomerSave[CalculationSectionType.Insurer] === true ||
            this.forceInsurerCustomerSave[CalculationSectionType.Owner] === true ||
            this.forceInsurerCustomerSave[CalculationSectionType.CoOwner] === true ||
            this.forceInsurerCustomerSave[CalculationSectionType.User] === true) {
            return true;
        } else {
            return false;
        }
    }

    private getAttributesByType(sectionName: string): any[] {
        const { product, inputsTypeValuePairs } = this.props;

        return (product && product.ProductAttributeMappings ? this.filteredProductAttributeMappings.map((attr: any, i: number) => {
            let attribute: any = attr.ProductAttribute;
            let tempUserFields: any = null;
            let customControlTypeUserField: any;

            if(attr.section !== sectionName) {
                return false;
            }
            
            if(attribute.UserFields) {
                tempUserFields = attribute.UserFields;
                customControlTypeUserField = tempUserFields.filter((userField: any) => userField.Key === 'customControlType');
            }

            let skipRender: boolean = false;
            let control: any;
            let options: any = {
                dropdown: [] as IDropdownOption[],
                choicegroup: [] as IChoiceGroupOption[],
                tableInputs: {} as any,
                combobox: [] as IComboBoxOption[],
            };
            let textFieldRows = 1;
            this.prevTempInputErrors[sectionName] = this.tempInputErrors[sectionName];

            if (attr.DefaultValue && !inputsTypeValuePairs[attr.Id]) {
                this.inputsToSet.push({ id: attr.Id, value: attr.DefaultValue });
            }

            let controlType: string = customControlTypeUserField && customControlTypeUserField.length > 0 ? customControlTypeUserField[0].Value : attr.AttributeControlTypeId;
            switch (controlType) {
                case "Datepicker":
                    control = Controls.Date;
                    break;
                case "Timepicker":
                    control = Controls.Time;
                    break;
                case "TextBox":
                    control = Controls.Text;
                    break;
                case "MultilineTextbox":
                    control = Controls.Text;
                    textFieldRows = 5;
                    break;
                case "RadioList":
                    control = Controls.ChoiceGroup;
                    options.choicegroup = attr.ProductAttributeValues.map((attrValue: any) => {
                        if (!inputsTypeValuePairs[attr.Id] && !this.inputsToSet.some(e => e.id === attr.Id) && attrValue.IsPreSelected) {
                            this.inputsToSet.push({ id: attr.Id, value: attrValue.Id });
                        }

                        return {
                            key: attrValue.Id, name: attrValue.Name,
                            text: attrValue.Locales && attrValue.Locales.length > 0 ? getLocaleName(attrValue.Locales, this.props.gnLanguage) : attrValue.Name,
                            checked: inputsTypeValuePairs[attr.Id] && inputsTypeValuePairs[attr.Id] === attrValue.Id ? true : attrValue.IsPreSelected,
                            disabled: false
                        }
                    }) as IChoiceGroupOption[];
                    break;
                case "Checkboxes":
                    control = Controls.CheckBoxOptions;
                    options.dropdown = attr.ProductAttributeValues.map((attrValue: any) => {
                        if (!inputsTypeValuePairs[attr.Id] && !this.inputsToSet.some(e => e.id === attr.Id) && attrValue.IsPreSelected) {
                            this.inputsToSet.push({ id: attr.Id, value: attrValue.Id });
                        }

                        return {
                            key: attrValue.Id, name: attrValue.Name,
                            text: attrValue.Locales && attrValue.Locales.length > 0 ? getLocaleName(attrValue.Locales, this.props.gnLanguage) : attrValue.Name,
                            isSelected: inputsTypeValuePairs[attr.Id] ? inputsTypeValuePairs[attr.Id][attrValue.Id] : attrValue.IsPreSelected
                        };
                    }) as IDropdownOption[];
                    break;
                case "DropdownList":
                    if (isCountriesInput(attr)) {
                        control = Controls.CountriesComboBox;
                    } else {
                        control = Controls.Picker;
                    }
                    options.dropdown = attr.ProductAttributeValues.map((attrValue: any) => {
                        if (!inputsTypeValuePairs[attr.Id] && !this.inputsToSet.some(e => e.id === attr.Id) && attrValue.IsPreSelected) {
                            this.inputsToSet.push({ id: attr.Id, value: attrValue.Id });
                        }
                        return {
                            key: attrValue.Id, name: attrValue.Name,
                            text: attrValue.Locales && attrValue.Locales.length > 0 ? getLocaleName(attrValue.Locales, this.props.gnLanguage) : attrValue.Name,
                            isSelected: inputsTypeValuePairs[attr.Id] === attrValue.Id ? true : attrValue.IsPreSelected
                        };
                    }) as IDropdownOption[];
                    break;
                case "ColorSquares":
                case "Table":
                    control = Controls.TableInputs;

                    if (this.exampleDataLoadedFlag) {
                        skipRender = true;
                        this.exampleDataLoadedFlag = false;
                    }
                    break;
                case "CountrySearchList":
                    control = Controls.CountrySearchList;
                    break;
            }

            let attrName: string = "";
            let iconData: any = {};
            let validationData: any = {};
            let inputError: string = "";
            let forceInputDisable: boolean = false;

            if(attribute.UserFields) {
                tempUserFields = attribute.UserFields;

                let attrKey = this.getAttrKey(attribute.UserFields);
                let splittedKey = attrKey.split('.');

                if (attrKey.length > 0 && splittedKey[splittedKey.length - 1] === "Pesel" &&
                    (inputsTypeValuePairs[attr.Id] && inputsTypeValuePairs[attr.Id].length > 0)) {
                    if (inputsTypeValuePairs[attr.Id].match(/^[0-9]+$/) !== null) {
                        if (validatePesel(inputsTypeValuePairs[attr.Id]) === false) {
                            inputError = L("Pesel is not valid.");
                            this.tempInputErrors[sectionName]++;
                        }
                    } else {
                        this.tempInputErrors[sectionName]++;
                    }
                }

                if (attrKey.length > 0 && splittedKey[splittedKey.length - 1] === "Nip" &&
                    (inputsTypeValuePairs[attr.Id] && inputsTypeValuePairs[attr.Id].length > 0)) {
                    if (inputsTypeValuePairs[attr.Id].match(/^[0-9]+$/) !== null) {
                        if (validateNip(inputsTypeValuePairs[attr.Id]) === false) {
                            inputError = L("NIP is not valid.");
                            this.tempInputErrors[sectionName]++;
                        }
                    } else {
                        this.tempInputErrors[sectionName]++;
                    }
                }

                if (attrKey.length > 0 && splittedKey[splittedKey.length - 1] === "Regon" &&
                    (inputsTypeValuePairs[attr.Id] && inputsTypeValuePairs[attr.Id].length > 0)) {
                    if (inputsTypeValuePairs[attr.Id].match(/^[0-9]+$/) !== null) {
                        if (validateRegon(inputsTypeValuePairs[attr.Id]) === false) {
                            inputError = L("REGON is not valid.");
                            this.tempInputErrors[sectionName]++;
                        }
                    } else {
                        this.tempInputErrors[sectionName]++;
                    }
                }

                // if(attrKey.length > 0 && splittedKey[splittedKey.length - 1] === "Email" && !!this.selectedClientContext[sectionName].customerId && 
                //     (inputsTypeValuePairs[attr.Id] && inputsTypeValuePairs[attr.Id].length > 0))
                // {
                //     forceInputDisable = true;
                // }
            }

            validationData = getInputValidationData(attribute);

            if (attribute.Locales) {
                attrName = getLocaleName(attribute.Locales, this.props.gnLanguage);
            } else {
                attrName = attribute.Name;
            }

            if (controlType === "TextBox") {
                iconData = getInputIconData(attribute);
            }

            if (controlType === "DropdownList") {
                if (isCountriesInput(attr)) {
                    let comboboxOptions: IComboBoxOption[] = getDropdownOptionsFromDataSource(attribute, this.props, "", this.props.gnLanguage, inputsTypeValuePairs[attr.Id], 'comboBox');
                    if (comboboxOptions && comboboxOptions.length > 0) {
                        options.combobox = comboboxOptions;
                    }
                } else {
                    let dropdownOptions: IDropdownOption[] = getDropdownOptionsFromDataSource(attribute, this.props, "Name", this.props.gnLanguage, inputsTypeValuePairs[attr.Id]);

                    if (dropdownOptions && dropdownOptions.length > 0) {
                        options.dropdown = generateDropdownOptionsIfCountriesInput(attr, dropdownOptions);
                    }
                }

            }

            if (controlType === "ColorSquares" || controlType === 'Table') {
                const tableOptions = getInputTableData(attribute, this.props.allUserFields, this.props.productAttributes, product);
                if (Object.keys(tableOptions).length > 0)
                    options.tableInputs = tableOptions;
            }

            if (attr.IsRequired && (!inputsTypeValuePairs[attr.Id] || inputsTypeValuePairs[attr.Id].length === 0)) {
                this.tempInputErrors[sectionName]++;
            }

            const property = new ContentViewModelProperty(attr.Id, attrName, control, attr.IsRequired, options, forceInputDisable, {
                isDataLoaded: this.props.isDataLoaded || this.props.asyncActionInProgress,
                rows: (validationData.multiline ? validationData.multiline : textFieldRows),
                textType: (validationData.inputType ? validationData.inputType : "text"),
                validationData: validationData,
                customPayload: sectionName,
                additionalMethod: (value: any, customPayload: any) => {
                    if (customPayload === CalculationSectionType.Standalone && value === '6226108d8a0b62f1cd55cdad') {
                        // if(value === '6226108d8a0b62f1cd55cdad') {
                        setTimeout(() => {
                            this.clearSectionTempErrors(CalculationSectionType.Owner);
                            this.forceInsurerCustomerSave[CalculationSectionType.Owner] = false;
                            this.forceUpdate();
                        }, 1010);
                        // }
                    }
                    // else {
                    //     this.throttledSetLastChangedSection(customPayload, value);
                    // }
                    this.throttledSetLastChangedSection(customPayload, value);
                }
            });

            if (attr.DisplayOrder < 0 || skipRender) { // attr hidden
                return false;
            }

            this.tempInputIdUserFieldPairs[attr.Id] = tempUserFields;

            let conditionalAttributeResult: any = conditionalAttribute(attr, this.props.inputsTypeValuePairs, this.tempInputIdUserFieldPairs, product.ProductAttributeMappings, this.props.productAttributes, this.props.gnLanguage);
            let now = + new Date();

            if (conditionalAttributeResult.newValue &&
                (!inputsTypeValuePairs[attr.Id] ||
                    (this.lastChangedSection === CalculationSectionType.Owner && this.whoIsOwnerChangeTimestamp !== null && now - this.whoIsOwnerChangeTimestamp <= 500)
                )
            ) {
                if (control === Controls.Picker || control === Controls.ChoiceGroup || control === Controls.CheckBoxOptions) {
                    const filteredAttrValue: any = filterBySome(attr.ProductAttributeValues, 'Name', conditionalAttributeResult.newValue);

                    if (filteredAttrValue && filteredAttrValue.Id && inputsTypeValuePairs[attr.Id] !== filteredAttrValue.Id) {
                        this.props.onInputChange(attr.Id, filteredAttrValue.Id, tempUserFields);
                    }
                } else if (inputsTypeValuePairs[attr.Id] !== conditionalAttributeResult.newValue) {
                    this.props.onInputChange(attr.Id, conditionalAttributeResult.newValue, tempUserFields);
                }
            }

            if (conditionalAttributeResult.show === true) {
                if (conditionalAttributeResult.disabled === true) {
                    property.disabled = true;
                }

                return renderElement(property, conditionalAttributeResult.show, iconData, this, { [attr.Id]: inputError });
            } else {
                if (this.tempInputErrors[sectionName] > this.prevTempInputErrors[sectionName]) {
                    this.tempInputErrors[sectionName]--;
                }
                delete this.props.inputsTypeValuePairs[attr.Id];
                return false;
            }
        }) : []);
    }

    private setSectionInputAttributes(lastChangedSection: string): any {
        let result = {
            shouldForceUpdate: false as boolean,
            tempInsurerAttributes: [] as any[],
        }

        // lastChangedSection = 'ALL';

        if (this.insurerAttributes.length === 0 || lastChangedSection === CalculationSectionType.Insurer || lastChangedSection === 'ALL') {
            this.clearSectionTempErrors(CalculationSectionType.Insurer);
            let tempInsurerAttributes = this.getAttributesByType(CalculationSectionType.Insurer).filter((x: any) => x !== false);
            this.insurerAttributes = tempInsurerAttributes;
            if (tempInsurerAttributes.length > 0) {
                result.shouldForceUpdate = true;
                result.tempInsurerAttributes = tempInsurerAttributes;
            }
        }

        this.lastChangedSection = "";
        return result;
    }

    render() {
        let { selectedClientData, tempSelectedClient, selectedClient, selectedProduct, asyncActionInProgress, product, customerTypeValuePairs, allProducts } = this.props;

        let shouldForceUpdate: boolean = false;

        // this.clearAllTempErrors();
        if (this.prevProductId !== product.Id) {
            this.componentMountTimestamp = + new Date();
        }

        if (this.filteredProductAttributes.length === 0 || this.prevProductId !== product.Id) {
            this.customerDataPreFilled = false;
            this.filterProductAttributes();
        }
        if ((this.filteredProductAttributeMappings.length === 0 && this.filteredProductAttributes.length > 0) || this.prevProductId !== product.Id) {
            this.filterProductMappings();
        }

        this.prevProductId = product.Id;

        if(this.productOptions.length === 0) {
            allProducts.forEach((product: any) => {
                if((AppConsts.allowedContent === 'ALL' || product.SeName === 'ubezpieczenie-auta') && product.Published) {
                    this.productOptions.push({ key: product.Id, text: product.Name });
                }
            });
        }

        let now = + new Date();
        if (this.componentMountTimestamp !== null && now - this.componentMountTimestamp > 1000) {
            for (let key in CalculationSectionType) {
                if (CalculationSectionType.hasOwnProperty(key) && key !== CalculationSectionType.Standalone) {
                    let EnumSectionType: string = CalculationSectionType[key];

                    if (customerTypeValuePairs[EnumSectionType] && Object.keys(customerTypeValuePairs[EnumSectionType]).length > 0) {
                        const enumSectionTypeHash: string = hash(customerTypeValuePairs[EnumSectionType]);

                        if (this.tempInputErrors[EnumSectionType] === 0) {
                            if (this.prevCustomerTypeValuePairsHash[EnumSectionType].length > 0 && enumSectionTypeHash !== this.prevCustomerTypeValuePairsHash[EnumSectionType]) {
                                if (tempSelectedClient === this.prevTempSelectedClient) {
                                    this.forceInsurerCustomerSave[EnumSectionType] = true;
                                } else {
                                    this.forceInsurerCustomerSave[EnumSectionType] = false;
                                }
                            }
                        } else {
                            this.forceInsurerCustomerSave[EnumSectionType] = false;
                        }
                        this.prevCustomerTypeValuePairsHash[EnumSectionType] = enumSectionTypeHash;
                    }
                }
            }
        }

        let setSectionInputsResult = this.setSectionInputAttributes(this.lastChangedSection);

        // Initialize local variables AFTER setSectionInputAttributes() has updated the instance attributes
        let insurerAttributes: any[] = this.insurerAttributes;

        // shouldForceUpdate = setSectionInputsResult.shouldForceUpdate;
        if (setSectionInputsResult.tempInsurerAttributes.length > 0) {
            insurerAttributes = setSectionInputsResult.tempInsurerAttributes;
        }

        if (this.props.inputErrors !== this.getSumOfAllTempErrors()) {
            this.props.setInputErrors(this.getSumOfAllTempErrors());
        }

        // if(blockNextStepButton === false && this.isAnySectionForcedToSave()) {
        //     this.props.setBlockNextStepButton(true, L('You must save insurer data before proceeding.'));
        // } else if(blockNextStepButton === true && !this.isAnySectionForcedToSave()) {
        //     this.props.setBlockNextStepButton(false);
        // }

        if (insurerAttributes && insurerAttributes.length > 0 && !!selectedClient && selectedClient.length > 0 && this.customerDataPreFilled === false) {
            this.customerDataPreFilled = true;
            this.hideInputs[CalculationSectionType.Insurer] = false;
            this.lastChangedSection = CalculationSectionType.Insurer;
            this.props.onTempCustomerSelect(selectedClient, true);
            this.props.onFillFormWithSelectedClientData(this.getFilteredProductAttributeMappingBySection(CalculationSectionType.Insurer));
        }

        if(selectedClient.length > 0 && selectedClientData && parseInt(this.prevSelectedClient) !== parseInt(selectedClientData.id) && 
            parseInt(selectedClientData.id) === parseInt(selectedClient)
        ) {
            this.preselectedClient = selectedClientData;

            this.selectedClientContext[CalculationSectionType.Insurer] = {
                id: parseInt(selectedClientData.id),
                customerId: selectedClientData.customerId,
                client: selectedClientData,
            };

            this.prevSelectedClient = selectedClient;
            this.props.onFillFormWithSelectedClientData(this.getFilteredProductAttributeMappingBySection(CalculationSectionType.Insurer));
        } else if(!selectedClient) {
            this.prevSelectedClient = '';
            this.preselectedClient = undefined;
        }

        this.prevTempSelectedClient = tempSelectedClient;

        if (shouldForceUpdate !== false) {
            this.forceUpdate();
        }

        return <>
            <Modal
                titleAriaId={'selectCustomerModal'}
                isOpen={this.isModalOpen}
                onDismiss={() => { this.isModalOpen = false; this.forceUpdate(); }}
                isBlocking={true}
                containerClassName={classNames.container}
            >
                <div className={classNames.header}>
                    {/* <span id={'selectCustomerModal'}>{L("Select customer:")}</span> */}
                    <IconButton
                        styles={iconButtonStyles}
                        iconProps={cancelIcon}
                        ariaLabel={L("Close popup modal")}
                        onClick={() => { this.isModalOpen = false; this.props.onTempCustomerSelect(undefined, false); }}
                    />
                </div>

                <div className={classNames.contentContainer}>
                    <PrimaryButton theme={myTheme} text={L('Fill in the form with the data of the selected customer')}
                        disabled={!this.selectedClientContext[this.activeSection] || this.selectedClientContext[this.activeSection].customerId.length === 0}
                        style={{marginBottom: 15, position: "absolute", right: 0, marginRight: 20}}
                        onClick={() => {
                            if (this.activeSection === CalculationSectionType.Insurer) {
                                this.customerDataPreFilled = true;
                                this.props.onCustomerSelect(tempSelectedClient, true);
                            }

                            this.selectedClientContext[this.activeSection] = {
                                id: parseInt(tempSelectedClient),
                                customerId: this.selectedClientContext[this.activeSection].customerId,
                                client: this.selectedClientContext[this.activeSection].client,
                            };
                            this.lastChangedSection = this.activeSection;

                            this.isModalOpen = false;
                            this.props.onFillFormWithSelectedClientData(this.getFilteredProductAttributeMappingBySection(this.activeSection));
                        }} 
                    />

                    <CustomerFluentListBaseWithCommandBar
                        searchText={undefined}
                        items={this.props.clientStore?.dataSet && this.props.clientStore?.dataSet.items ? this.props.clientStore?.dataSet.items : []}
                        store={this.props.clientStore!}
                        history={this.props.history}
                        scrollablePanelMarginTop={200}
                        customData={{
                            selectedClient: !!this.selectedClientFullname ? `${this.selectedClientFullname}` : undefined,
                            selectedClientId: this.selectedClientContext[this.activeSection] ? this.selectedClientContext[this.activeSection].id : undefined,
                            customLoadSpinnerStyle: {bottom: '-81px'},
                        }}
                        // customOnSearchTextChanged={(text: string) => { this.selectClientSearchText = text; this.forceUpdate(); }}
                        customSelection={this._clientListSelection}
                        customOnSelectionChanged={(selection: any) => {
                            if (typeof selection === 'string' && selection === 'deleteClient') {
                                this.selectedClientContext[this.activeSection] = {
                                    id: 0,
                                    customerId: '',
                                    client: {},
                                };
                                this.selectedClientFullname = '';
                                this.forceUpdate();
                            }
                        }}
                    />
                </div>
            </Modal>

            <Stack horizontal={true} horizontalAlign="start" verticalAlign="center" wrap={false} style={{paddingTop: 15}}>
                <DropdownBase key="ProductId" required={true} label={L("Select product")} options={this.productOptions} value={selectedProduct} 
                    disabled={asyncActionInProgress || typeof asyncActionInProgress === 'undefined'}
                    customLabelStyles={{ width: "150px", minWidth: "150px" }} isDataLoaded={this.productOptions.length > 0} onChange={this.props.onProductSelect}
                    labelContainerCustomStyles={{ marginTop: 0 }}
                />

                {asyncActionInProgress && (this.filteredProductAttributes.length > 0 && !!selectedProduct && selectedProduct === product.Id) && (
                    <Spinner label={this.props.loadSpinnerCustomLabel ? this.props.loadSpinnerCustomLabel : ''} className={classNames.smallLoadSpinner} size={SpinnerSize.small}
                        ariaLive="assertive" labelPosition="right" style={{marginLeft: 20}} />
                )}
            </Stack>

            {(this.filteredProductAttributes.length > 0 && !!selectedProduct && selectedProduct === product.Id) && <>
                {!!selectedProduct && selectedProduct === this.props.mapKeyToId("mapProductNameToProductId", "ubezpieczenie-auta") ?
                    <Stack>
                        <div className={`${classNames.sectionContainter} ${this.hideInputs[CalculationSectionType.Insurer] && classNames.sectionContainterHidden} section-container`}>
                            <Stack horizontal={true}>
                                {/* { (!this.hideInputs[CalculationSectionType.Insurer] && !!this.selectedClientContext[CalculationSectionType.Insurer].id) &&
                                    <MessageBar
                                    className={`${classNames.messageBar} ${classNames.messageBarMargin}`}
                                    messageBarType={MessageBarType.warning}
                                    isMultiline={false}>
                                    {`${L('Editing customer data:')} ${this.selectedClientContext[CalculationSectionType.Insurer].client.user.name} ${this.selectedClientContext[CalculationSectionType.Insurer].client.user.surname}`}
                                    </MessageBar>
                                } */}

                                <Stack.Item className={classNames.sectionContainerHeader}>
                                    <Text className={`${classNames.fontBold} ${classNames.sectionContainterTitle}`}>{L("insurer")}</Text>

                                    {asyncActionInProgress && (
                                        <Spinner label={''} className={classNames.smallLoadSpinner} size={SpinnerSize.small} ariaLive="assertive" labelPosition="right" />
                                    )}

                                    <Stack.Item>
                                        <DefaultButton id={`clearData${CalculationSectionType.Insurer}Button`} text={L("Clear all")} className={classNames.sectionIcon} disabled={asyncActionInProgress} onClick={() => { this.clearAllData(CalculationSectionType.Insurer); }} />
                                        <DefaultButton text={L("Select customer")} className={classNames.sectionIcon} disabled={asyncActionInProgress} onClick={() => { this.activeSection = CalculationSectionType.Insurer; this.isModalOpen = true; this.forceUpdate(); }} />
                                    </Stack.Item>
                                </Stack.Item>

                                    {/* <IconButton
                                        styles={iconButtonStyles}
                                        id={`saveCustomer${CalculationSectionType.Insurer}Button`}
                                        iconProps={{iconName: 'Save'}}
                                        ariaLabel={L("Save customer")}
                                        title={L("Save customer")}
                                        className={classNames.sectionIcon}
                                        disabled={asyncActionInProgress || this.tempInputErrors[CalculationSectionType.Insurer] > 0
                                                || this.forceInsurerCustomerSave[CalculationSectionType.Insurer] === false}
                                        onClick={() => { this.saveCustomer(CalculationSectionType.Insurer); }}
                                    />
                                    { this.isCalloutVisible[CalculationSectionType.Insurer] && <Callout
                                        coverTarget={false}
                                        ariaLabelledBy={`callout-label-${CalculationSectionType.Insurer}`}
                                        className={classNames.callout}
                                        onDismiss={() => { this.isCalloutVisible[CalculationSectionType.Insurer] = false; this.forceUpdate(); }}
                                        target={`#saveCustomer${CalculationSectionType.Insurer}Button`}
                                        isBeakVisible={true}
                                        setInitialFocus={false}
                                    >
                                        <Text block variant="xLarge" className={classNames.title} id={`callout-label-${CalculationSectionType.Insurer}`}>
                                            {this.calloutText}
                                        </Text>
                                        <div className={classNames.actions}>
                                            <DefaultButton onClick={() => {this.isCalloutVisible[CalculationSectionType.Insurer] = false; this.forceUpdate();}} text={L("Close")} />
                                        </div>
                                    </Callout> } */}
                            </Stack>

                            {this.hideInputs[CalculationSectionType.Insurer] === true ?
                                <Stack wrap={true}>
                                    <p><span>{L("Customer")}: </span>
                                        {this.preselectedClient && !!this.preselectedClient.user.name ? `${this.preselectedClient.user.name} ${!!this.preselectedClient.user.surname && this.preselectedClient.user.surname}` : `-`}</p>
                                    <p><span>{L("Pesel")}: </span>{`${this.preselectedClient && !!this.preselectedClient.pesel ? this.preselectedClient.pesel : '-'}`}</p>
                                    <p><span>{L("E-mail")}: </span>{`${this.preselectedClient && !!this.preselectedClient.user.emailAddress ? this.preselectedClient.user.emailAddress : '-'}`}</p>
                                    <p><span>{L("Company name")}: </span>{`${this.preselectedClient && !!this.preselectedClient.company ? this.preselectedClient.company : '-'}`}</p>
                                    <p><span>{L("NIP")}: </span>{`${this.preselectedClient && !!this.preselectedClient.nip ? this.preselectedClient.nip : '-'}`}</p>
                                    <p><span>{L("REGON")}: </span>{`${this.preselectedClient && !!this.preselectedClient.regon ? this.preselectedClient.regon : '-'}`}</p>
                                </Stack>
                                :
                                <Stack horizontal={true} horizontalAlign="space-between" wrap={true}>
                                    {insurerAttributes && insurerAttributes.length > 0 ?
                                        <>
                                            <Stack style={{ marginRight: '25px' }}>{insurerAttributes.map((attr: any, index: number) => {
                                                if (index < insurerAttributes.length / 2) {
                                                    return attr;
                                                }
                                                return '';
                                            })}</Stack>
                                            {insurerAttributes.length > 1 &&
                                                <Stack className="section-content-wrapper--half">
                                                    {insurerAttributes.map((attr: any, index: number) => {
                                                        if (index >= insurerAttributes.length / 2) {
                                                            return attr;
                                                        }
                                                        return '';
                                                    })}
                                                </Stack>
                                            }
                                        </>
                                        :
                                        <Spinner label={L('Please wait...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="top" />
                                    }</Stack>
                            }
                        </div>
                    </Stack>
                    :
                    <Stack>
                        <div className={`${classNames.sectionContainter} section-container`}>
                            <Stack horizontal={true}>
                                {/* { (!this.hideInputs[CalculationSectionType.Insurer] && !!this.selectedClientContext[CalculationSectionType.Insurer].id) &&
                                    <MessageBar
                                    className={`${classNames.messageBar} ${classNames.messageBarMargin}`}
                                    messageBarType={MessageBarType.warning}
                                    isMultiline={false}>
                                    {`${L('Editing customer data:')} ${this.selectedClientContext[CalculationSectionType.Insurer].client.user.name} ${this.selectedClientContext[CalculationSectionType.Insurer].client.user.surname}`}
                                    </MessageBar>
                                } */}
                                
                                <Stack.Item className={classNames.sectionContainerHeader}>
                                    <Text className={`${classNames.fontBold} ${classNames.sectionContainterTitle}`}>{L("insurer")}</Text>

                                    {asyncActionInProgress && (
                                        <Spinner label={''} className={classNames.smallLoadSpinner} size={SpinnerSize.small} ariaLive="assertive" labelPosition="right" />
                                    )}

                                    <Stack.Item>
                                        <DefaultButton id={`clearData${CalculationSectionType.Insurer}Button`} text={L("Clear all")} ariaLabel={L("Clear all")} className={classNames.sectionIcon} disabled={asyncActionInProgress} onClick={() => { this.clearAllData(CalculationSectionType.Insurer); }} />
                                        <DefaultButton text={L("Select customer")} ariaLabel={L("Select customer")} className={classNames.sectionIcon} disabled={asyncActionInProgress} onClick={() => { this.activeSection = CalculationSectionType.Insurer; this.isModalOpen = true; this.forceUpdate(); }} />
                                    </Stack.Item>
                                </Stack.Item>
                                <>
                                    {/* <IconButton
                                        styles={iconButtonStyles}
                                        id={`saveCustomer${CalculationSectionType.Insurer}Button`}
                                        iconProps={{iconName: 'Save'}}
                                        ariaLabel={L("Save customer")}
                                        title={L("Save customer")}
                                        className={classNames.sectionIcon}
                                        disabled={asyncActionInProgress || this.tempInputErrors[CalculationSectionType.Insurer] > 0
                                            || this.forceInsurerCustomerSave[CalculationSectionType.Insurer] === false}
                                        onClick={() => { this.saveCustomer(CalculationSectionType.Insurer); }}
                                    />
                                    { this.isCalloutVisible[CalculationSectionType.Insurer] && <Callout
                                        coverTarget={false}
                                        ariaLabelledBy={`callout-label-${CalculationSectionType.Insurer}`}
                                        className={classNames.callout}
                                        onDismiss={() => { this.isCalloutVisible[CalculationSectionType.Insurer] = false; this.forceUpdate(); }}
                                        target={`#saveCustomer${CalculationSectionType.Insurer}Button`}
                                        isBeakVisible={true}
                                        setInitialFocus={false}
                                    >
                                        <Text block variant="xLarge" className={classNames.title} id={`callout-label-${CalculationSectionType.Insurer}`}>
                                            {this.calloutText}
                                        </Text>
                                        <div className={classNames.actions}>
                                            <DefaultButton onClick={() => {this.isCalloutVisible[CalculationSectionType.Insurer] = false; this.forceUpdate();}} text={L("Close")} />
                                        </div>
                                    </Callout> } */}
                                </>
                            </Stack>

                            <Stack horizontal={true} horizontalAlign="space-between" wrap={true}>
                                {insurerAttributes && insurerAttributes.length > 0 ?
                                    <>
                                        <Stack style={{ marginRight: '25px' }}>{insurerAttributes.map((attr: any, index: number) => {
                                            if (index < insurerAttributes.length / 2) {
                                                return attr;
                                            }
                                            return '';
                                        })}</Stack>
                                        {insurerAttributes.length > 1 &&
                                            <Stack className="section-content-wrapper--half">
                                                {insurerAttributes.map((attr: any, index: number) => {
                                                    if (index >= insurerAttributes.length / 2) {
                                                        return attr;
                                                    }
                                                    return '';
                                                })}
                                            </Stack>
                                        }
                                    </>
                                    :
                                    <Spinner label={L('Please wait...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="top" />
                                }
                            </Stack>
                        </div>
                    </Stack>
                }</>
            }
        </>;
    }
}