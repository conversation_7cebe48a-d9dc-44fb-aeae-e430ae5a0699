import { addYears, ComboBox, FocusZone, FocusZoneDirection, FocusZoneTabbableElements, FontWeights, IChoiceGroupOption, IComboBox, IComboBoxOption, IDropdownOption, mergeStyleSets, Pivot, PivotItem, Spinner, SpinnerSize } from "@fluentui/react";
import React from "react";
import { L } from "../../../lib/abpUtility";
import AppConsts from "../../../lib/appconst";
import {additionalTheme, myTheme} from "../../../styles/theme";
import { getDropdownOptionsFromDataSource, getInputIconData, getInputTableData, getInputValidationData, isInputInTab, shouldSaveAsTemplateForTable, isCountriesInput, isInputFastCalculation, inputHasUserFieldKey } from "../../../utils/inputUtils";
import { getAbpLanguage, getLocaleName } from "../../../utils/languageUtils";
import { analyzeAttributeDependencies, conditionalAttribute, createInputsStateKey, getConditionalResultFromAttrJsonProps, renderElement } from "../../../utils/policyCalculationUtils";
import { generateDropdownOptionsIfCountriesInput } from "../../../utils/storeUtils";
import { filterBySome, isJsonString } from "../../../utils/utils";
import { ContentViewModelProperty } from "../../BaseComponents/contentViewBase";
import { Controls } from "../../BaseComponents/controls";
import { LabelContainerComponent } from "../../BaseComponents/labelContainerComponent";
import { LabelComponent } from "../../BaseComponents/labelComponent";

var _ = require('lodash');

const classNames = mergeStyleSets({
    hide: {
        display: 'none !important',
    },
    toolbar: {
        selectors: {
            '& .ms-Pivot': {
                display: 'none',
            }
        }
    },
    messageBar: {
        width: 'fit-content'
    },
    loadSpinner: {
        display: 'inline-flex',
        marginTop: '15px',
        marginLeft: 'auto',
        marginRight: 'auto',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    loadSpinnerTopBar: {
        display: 'inline-flex',
        marginLeft: '15px',
        marginRight: 'auto',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    toggleSlider: {
        marginLeft: '25px',
        display: 'flex',
        alignContent: 'center',
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        border: `1px solid ${myTheme.palette.neutralSecondaryAlt}`,
        padding: '1px 16px',
        borderRadius: '2px',
        transition: 'background 150ms',
        background: myTheme.palette.white,
        selectors: {
            '& .ms-Slider-container': {
                cursor: 'grab',
                width: '40px',
                marginLeft: '10px',
            }
        }
    },
    toggleSliderActive: {
        background: myTheme.palette.themeLight,
    },
    container: {
        display: 'flex',
        flexFlow: 'column nowrap',
        alignItems: 'stretch',
        justifyContent: 'center',
        width: '75%',
        overflowX: 'hidden',
    },
    header: [
        myTheme.fonts.large,
        {
            flex: '1 1 auto',
            color: myTheme.palette.neutralPrimary,
            display: 'flex',
            alignItems: 'center',
            fontWeight: FontWeights.semibold,
            padding: '12px 12px 14px 24px',
        },
    ],
    contentContainer: {
        width: '100%',
        height: 'auto',
        padding: '0 20px 40px 20px',
        boxSizing: 'border-box',
        selectors: {
            '> div': {
                marginRight: '0',
                width: '99.9%',
                maxWidth: '99.9%',
                selectors: {
                    '> div': {
                        width: '99.9%',
                        maxWidth: '99.9%',
                    }
                }
            }
        }
    },
    modalActionButton: {
        display: 'block',
        margin: '30px auto 0',
    },
    myTabItem: {
        display: 'flex'
        // '& div > div:first-child': {
        //     width: '320px !important',
        //     minWidth: '320px !important',
        //     marginRight: '50px !important'
        // },
        // '& div.checkboxLabel-62': {
        //     width: 'auto !important',
        // },
        // '& div.checkboxOptionsWrapper-64 div': {
        //     width: 'auto !important',
        //     minWidth: 'auto !important',
        //     marginLeft: '6px !important',
        // }
    },
    comboBoxStyles: {
        width: '300px',
        position: 'relative',
    },
});

export interface IApkStep2Props {
    product: any;
    isDataLoaded: boolean;
    inputsTypeValuePairs: any;
    inputsIdUserFieldsPairs: any;
    gnLanguage: any;
    customInputsData: any;
    inputErrors: number;
    allUserFields: any[];
    isEditMode: boolean;
    vehicleOptions: any;
    asyncActionInProgress?: boolean;
    savedTemplateInputsForTable: any;
    savedMappedIdsForLaterUse: any;
    customPresetredInputOptions: any;
    isFastCalculation: boolean;
    inputErrorsText: any;
    inputValuePairsStringified: string;
    hiddenInputsTypeValuePairs: any;
    productAttributes: any;
    setInputErrors: (errorsCount: number) => void;
    onInputChange: (id: string, value: any, userFields: any) => void;
    onMassInputChange: (inputFields: any, userFields: any) => void;
    toggleAsyncActionFlag: (newState: boolean, forceUpdate: boolean) => void;
    mapKeyToId: (mapType: string, key: string) => string;
    setSavedTemplateInputsForTable: (value: any) => void;
    setHiddenInputsTypeValuePairs: (id: string, value: any) => void;
    setInputsUserFields: (id: string, userFields: any) => void;
}

export class ApkStep2 extends React.Component<IApkStep2Props> {
    private tabKeyIndexHelper: any = {};
    private tabs: any[] = [
        // { key: UserField.Key, name: displayName, items: [{value, order}, {value, order}] }
    ];
    private tabsParsed: boolean = false;
    private initialReRender: boolean = false;
    private inputsToSet: any[] = [];
    private exampleDataLoadedFlag: boolean = false;
    private templateInputsForTable: any = {};
    private tempInputIdUserFieldPairs: any = {};
    private prevTempInputErrors: number = 0;
    private tempInputErrors: number = 0;
    private tabInputsInEditModeSet: boolean = false;
    private lastRenderTimestamp: number = 0;
    private lastSetDefaultValueTimestamp: number = 0;
    private conditionalDefaultValueAlreadySetFor: any[] = [];
    private debouncedOnInputChange: any = _.debounce((inputKey: string, value: any, userFields: any, customPayload?: any) => {
        this.props.onInputChange(inputKey, value, userFields);
    }, AppConsts.defaultInputsDelay, []);
    private prevConditionalOptionsResult: any = {};
    private savedAttributes: any = {};
    private prevInputValuePairsStringified: string = '';
    private prevInputErrorsTextStringified: string = '';
    private prevCustomInputsDataStringified: string = '';
    private prevAsyncActionInprogress: boolean | undefined = undefined;
    private conditionalAttributeCache: Map<string, any> = new Map();
    private inputDependencies: Map<string, Set<string>> = new Map();
    private keyIdPairs: Map<string, string> = new Map();

    componentDidMount() {
        this.setDefaultInputsData();

        if(!this.initialReRender) {
            this.initialReRender = true;
            this.forceUpdate();
        }
    }

    private setDefaultInputsData() {
        if(this.inputsToSet.length > 0) {
            const cloneInputsTypeValuePairs = {};
            const cloneInputsIdUserFieldsPairs = {};
            
            this.inputsToSet.forEach((element) => {
                // this.props.onInputChange(element.id, element.value, this.tempInputIdUserFieldPairs[element.id]);
                cloneInputsTypeValuePairs[element.id] = element.value ? 
                                                        (isJsonString(element.value) ? 
                                                            JSON.parse(element.value) : element.value) 
                                                        : element.value;
                cloneInputsIdUserFieldsPairs[element.id] = this.tempInputIdUserFieldPairs[element.id];
            });

            this.props.onMassInputChange(cloneInputsTypeValuePairs, cloneInputsIdUserFieldsPairs);
            this.inputsToSet = [];
        }
    }

    private setConditionalDefaultInputsData(inputsToSet: any[]) {
        if(inputsToSet.length > 0) {
            inputsToSet.forEach((element) => {
                this.props.onInputChange(element.id, element.value, element.userFields);
            });
            inputsToSet = [];
        }
    }

    private setTabs(UserFields: any) {
        let tabKeys: string[] = [];
        let tabNames: string[] = [];

        UserFields.forEach((UserField: any) => {    
            if(UserField.Key === "tabsName") {
                tabNames = UserField.Value.split(';;');

                tabNames.forEach((tabName, i) => {
                    let splittedTabNames = tabName.split(';');
                    splittedTabNames.forEach((splittedTabName) => {
                        let translatedNames = splittedTabName.split('=');
                        if(translatedNames[0].toLowerCase() === getAbpLanguage()) {
                            tabNames[i] = translatedNames[1];
                        }
                    });
                });
            } else if(UserField.Key === "tabs") {
                tabKeys = UserField.Value.split(';');
                if(tabNames.length <= 0) {
                    tabNames = tabKeys;
                }
            }
            
        });
        
        for(let i: number = 0; i < tabKeys.length; i++) {
            if(tabKeys[i].length > 0) {
                this.tabs.push({ key: tabKeys[i], name: tabNames[i], items: []});
                this.tabKeyIndexHelper[tabKeys[i]] = this.tabs.length - 1;
            }
        };
    }

    private parseTabs(UserFields: any, attributeElement: any, attrId: string, attributeId: string, updateValue: boolean) {
        let tabUserField: any = {};
        let hideAttr: boolean = false;

        UserFields.forEach((UserField: any) => {
            if(UserField.Key === "Hide" && UserField.Value === "true") {
                hideAttr = true;
            }

            if(UserField.Key.substr(0, 4) === "step" && UserField.Key.substr(0, 5) !== "step_") {
                tabUserField = UserField;
            }
        });

        if(Number.isInteger(this.tabKeyIndexHelper[tabUserField.Key]) && (!hideAttr || this.props.isEditMode) && typeof attributeElement !== 'undefined') {
            if(updateValue) {
                let attrUpdated = false;
                this.tabs[this.tabKeyIndexHelper[tabUserField.Key]].items.some((item: any) => {
                    if(item.id === attributeId) {
                        item.value = attributeElement;
                        attrUpdated = true;
                        return true;
                    }
                    return false;
                });

                if(!attrUpdated) {
                    this.tabs[this.tabKeyIndexHelper[tabUserField.Key]].items.push({ id: attributeId, value: attributeElement, order: tabUserField.Value, hide: hideAttr });
                }
            } else {
                this.tabs[this.tabKeyIndexHelper[tabUserField.Key]].items.push({ id: attributeId, value: attributeElement, order: tabUserField.Value, hide: hideAttr });
            }
        } else if(!attributeElement) {
            let indexToDelete = -1;

            this.tabs[this.tabKeyIndexHelper[tabUserField.Key]].items.some((item: any, index: number) => {
                if(item.id === attributeId) {
                    indexToDelete = index;
                    return true;
                }
                return false;
            });

            if(indexToDelete >= 0) {
                delete this.tabs[this.tabKeyIndexHelper[tabUserField.Key]].items[indexToDelete];
                delete this.props.inputsTypeValuePairs[attrId];
            }
        }
        // else { ### TODO create additional L("Other") tab and store there attributes without tab Key
        //     this.tabs.push({ name: UserField.Key, items: [{ value: attributeElement, order: UserField.Value }]});
        //     this.tabKeyIndexHelper[UserField.Key] = this.tabs.length - 1;
        // }
    }

    private deleteFromTab(ProductAttributeId: string) {
        if(this.tabs && this.tabs.length > 0) {
            this.tabs.some((tab: any, tabIndex: number) => {
                let itemFound: boolean = false;

                if(tab.items) {
                    tab.items.some((item: any, itemIndex: number) => {
                        if(item.id === ProductAttributeId) {
                            this.tabs[tabIndex].items.splice(itemIndex, 1);
                            itemFound = true;
                            return true;
                        }
                        return false;
                    });
                }

                if(itemFound) {
                    return true;
                }
                return false;
            });
        }
    }

    private sortByOrder(a: any, b: any) {
        a.order = parseInt(a.order);
        b.order = parseInt(b.order);

        if (a.order < b.order){
            return -1;
        }
        if (a.order > b.order){
            return 1;
        }
        return 0;
    }

    private mapAttributes(): any { 
        const {inputsTypeValuePairs, inputsIdUserFieldsPairs, productAttributes, product, isDataLoaded, gnLanguage, savedMappedIdsForLaterUse, customPresetredInputOptions, customInputsData} = this.props;

        this.tempInputErrors = 0;

        let shouldMassUpdateInputs: boolean = false;
        let inputsToSet: any[] = [];
        let now = + new Date();

        let attributes: any = (product && product.ProductAttributeMappings ? product.ProductAttributeMappings!.map((attr: any, i: number) => {   
            let skipRender: boolean = false;
            let control: any;
            let options: any = {
                dropdown: [] as IDropdownOption[],
                choicegroup: [] as IChoiceGroupOption[],
                tableInputs: {} as any,
            };
            let textFieldRows = 1;
            let tempUserFields: any = null;
            let attribute: any = attr.ProductAttribute;
            let customControlTypeUserField: any;
            let conditionalOptionsResult: any = {};
            let tempDefaultValue: string = '';

            this.prevTempInputErrors = this.tempInputErrors;

            attribute['attrId'] = attr.Id;

            if(attribute.UserFields) {
                tempUserFields = attribute.UserFields;
                customControlTypeUserField = tempUserFields.filter((userField: any) => userField.Key === 'customControlType');
                
                const itemsAttrJsonPropsUserField: any = filterBySome(tempUserFields, "Key", "items_attr_json_props");
                if(itemsAttrJsonPropsUserField && itemsAttrJsonPropsUserField.Value) {
                    const parsedItemsAttrJsonPropsUserField: any = JSON.parse(itemsAttrJsonPropsUserField.Value);
                    for(let optionName in parsedItemsAttrJsonPropsUserField) {
                        conditionalOptionsResult[optionName] = getConditionalResultFromAttrJsonProps(parsedItemsAttrJsonPropsUserField[optionName], this.props.inputsTypeValuePairs, 
                                                    this.props.product.ProductAttributeMappings, productAttributes, this.props.gnLanguage, true);
                    }
                }
            }

            if(attr.DefaultValue && !inputsTypeValuePairs[attr.Id]) {
                this.inputsToSet.push({id: attr.Id, value: attr.DefaultValue});
                tempDefaultValue = attr.DefaultValue;
            } else if(!!tempUserFields) {
                const defaultValueUserField: any = filterBySome(tempUserFields, "Key", "defaultValue");
                if(defaultValueUserField && defaultValueUserField.Value && !inputsTypeValuePairs[attr.Id]) {
                    this.inputsToSet.push({id: attr.Id, value: defaultValueUserField.Value});
                    tempDefaultValue = defaultValueUserField.Value;
                }
            }

            let controlType: string = customControlTypeUserField && customControlTypeUserField.length > 0 ? customControlTypeUserField[0].Value : attr.AttributeControlTypeId;
            switch(controlType) {
                case "Datepicker":
                    control = Controls.Date;
                    break;
                case "Timepicker":
                    control = Controls.Time;
                    break;
                case "TextBox":
                    control = Controls.Text;
                    break;
                case "MultilineTextbox":
                    control = Controls.Text;
                    textFieldRows = 5;
                    break;
                case "RadioList":
                    control = Controls.ChoiceGroup;
                    options.choicegroup = attr.ProductAttributeValues.map((attrValue: any) => {
                        if(!inputsTypeValuePairs[attr.Id] && !this.inputsToSet.some(e => e.id === attr.Id) && attrValue.IsPreSelected && !this.tabsParsed) {
                            this.inputsToSet.push({id: attr.Id, value: attrValue.Id});
                        }

                        return { key: attrValue.Id, name: attrValue.Name,
                                text: attrValue.Locales && attrValue.Locales.length > 0 ? getLocaleName(attrValue.Locales, gnLanguage) : attrValue.Name, 
                                checked: inputsTypeValuePairs[attr.Id] && inputsTypeValuePairs[attr.Id] === attrValue.Id ? true : attrValue.IsPreSelected,
                                disabled: typeof conditionalOptionsResult[attrValue.Name] !== 'undefined' ? conditionalOptionsResult[attrValue.Name].disabled : false,
                                hidden: typeof conditionalOptionsResult[attrValue.Name] !== 'undefined' ? !conditionalOptionsResult[attrValue.Name].show : false }
                    }) as IChoiceGroupOption[];
                    break;
                case "Checkboxes":
                    control = Controls.CheckBoxOptions;
                    options.dropdown = attr.ProductAttributeValues.map((attrValue: any) => {
                        if(!inputsTypeValuePairs[attr.Id] && !this.inputsToSet.some(e => e.id === attr.Id) && attrValue.IsPreSelected && !this.tabsParsed) {
                            this.inputsToSet.push({id: attr.Id, value: attrValue.Id});
                        }

                        return { key: attrValue.Id, name: attrValue.Name,
                                text: attrValue.Locales && attrValue.Locales.length > 0 ? getLocaleName(attrValue.Locales, gnLanguage) : attrValue.Name, 
                                isSelected: inputsTypeValuePairs[attr.Id] ? inputsTypeValuePairs[attr.Id][attrValue.Id] : attrValue.IsPreSelected,
                                disabled: typeof conditionalOptionsResult[attrValue.Name] !== 'undefined' ? conditionalOptionsResult[attrValue.Name].disabled : false,
                                hidden: typeof conditionalOptionsResult[attrValue.Name] !== 'undefined' ? !conditionalOptionsResult[attrValue.Name].show : false };
                    }) as IDropdownOption[];
                    break;
                case "DropdownList":
                case "MultiDropdownList":
                    control = controlType === 'MultiDropdownList' ? Controls.MultiPicker : Controls.Picker;
                    options.dropdown = attr.ProductAttributeValues.map((attrValue: any) => {
                        if(!inputsTypeValuePairs[attr.Id] && !this.inputsToSet.some(e => e.id === attr.Id) && attrValue.IsPreSelected && !this.tabsParsed) {
                            this.inputsToSet.push({id: attr.Id, value: attrValue.Id});
                        }
                        return { key: attrValue.Id, name: attrValue.Name,
                                text: attrValue.Locales && attrValue.Locales.length > 0 ? getLocaleName(attrValue.Locales, gnLanguage) : attrValue.Name, 
                                isSelected: attrValue.IsPreSelected,
                                disabled: typeof conditionalOptionsResult[attrValue.Name] !== 'undefined' ? conditionalOptionsResult[attrValue.Name].disabled : false,
                                hidden: typeof conditionalOptionsResult[attrValue.Name] !== 'undefined' ? !conditionalOptionsResult[attrValue.Name].show : false };
                    }) as IDropdownOption[];
                    break;
                case "ColorSquares":
                case "Table":
                    control = Controls.TableInputs;

                    if(typeof inputsTypeValuePairs[attr.Id] === 'undefined') {
                        this.inputsToSet.push({id: attr.Id, value: ''});
                    }

                    if(this.exampleDataLoadedFlag) {
                        skipRender = true;
                        this.exampleDataLoadedFlag = false;
                    }
                    break;
                case "CountrySearchList":
                    control = Controls.CountrySearchList;
                    break;
                case "SportSearchList":
                    control = Controls.SportSearchList;
                    break;
            }

            let attrName: string = "";
            let iconData: any = {};
            let validationData: any = {};
            let isInTab: boolean = false;
            let isFastCalculation: boolean = false;
            let saveAsTemplateForTable: string | boolean = false;

            validationData = getInputValidationData(attribute, customInputsData);

            if(!!tempDefaultValue) {
                validationData['defaultValue'] = tempDefaultValue;
            }

            if(control === Controls.CountrySearchList) {
                validationData['disableGetAllOnMount'] = true;
            }

            if(attr.Id === this.props.savedMappedIdsForLaterUse.travelEndDateId && !!inputsTypeValuePairs[this.props.savedMappedIdsForLaterUse.travelStartDateId]) {
                const tempStartDate: Date = new Date(inputsTypeValuePairs[this.props.savedMappedIdsForLaterUse.travelStartDateId]);
                let newEndDate: Date = addYears(tempStartDate, 1);
                newEndDate.setDate(newEndDate.getDate() - 1);
                validationData['maxDate'] = newEndDate;
            }

            if(attribute.UserFields) {
                if((!inputHasUserFieldKey(tempUserFields, "is_for_apk") && !inputHasUserFieldKey(tempUserFields, "is_only_for_apk"))
                    || inputHasUserFieldKey(tempUserFields, "not_for_apk")
                ) {
                    return false;
                }
                
                isInTab = isInputInTab(attribute.UserFields);
                isFastCalculation = isInputFastCalculation(attribute.UserFields);
                saveAsTemplateForTable = shouldSaveAsTemplateForTable(attr.Id, attribute.UserFields, this.templateInputsForTable, productAttributes, this.props.allUserFields, product);
                if(this.prevConditionalOptionsResult[attr.Id] !== JSON.stringify(conditionalOptionsResult)) {
                    this.prevConditionalOptionsResult[attr.Id] = JSON.stringify(conditionalOptionsResult);
                }
            }

            if(attribute.Locales) {
                attrName = getLocaleName(attribute.Locales, gnLanguage);
            } else {
                attrName = attribute.Name;
            }

            if(controlType === "TextBox" || controlType === "ColorSquares" || controlType === "Table" || controlType === "DropdownList") {
                iconData = getInputIconData(attribute);
            }

            if(controlType === "DropdownList") {
                let dropdownOptions: IDropdownOption[] = [];
                if(isCountriesInput(attr)) {
                    dropdownOptions = getDropdownOptionsFromDataSource(attribute, this.props, "", this.props.gnLanguage);
                } else {
                    dropdownOptions = getDropdownOptionsFromDataSource(attribute, this.props, "Name", this.props.gnLanguage);
                }

                if(dropdownOptions && dropdownOptions.length > 0) {
                    options.dropdown = generateDropdownOptionsIfCountriesInput(attr, dropdownOptions);
                }
            }

            if(controlType === "ColorSquares" || controlType === "Table") {
                const tableOptions = getInputTableData(attribute, this.props.allUserFields, productAttributes, product);
                if(Object.keys(tableOptions).length > 0)
                    options.tableInputs = tableOptions;
            }

            if((attr.IsRequired || validationData.required) && (!inputsTypeValuePairs[attr.Id] || inputsTypeValuePairs[attr.Id].length === 0)) {
                this.tempInputErrors++;
            }

            if([savedMappedIdsForLaterUse.autoDrivingLicenceIssueYear, savedMappedIdsForLaterUse.autoYearOfPurchaseOfTheVehicle].includes(attr.Id) && Array.isArray(customPresetredInputOptions[attr.Id])) {
                control = Controls.Picker;
                options.dropdown = customPresetredInputOptions[attr.Id];
            }

            const property = new ContentViewModelProperty(attr.Id, attrName, control, attr.IsRequired ? true : (validationData.required ? true : false), options, 
                                    (typeof this.props.asyncActionInProgress === 'boolean' && this.props.asyncActionInProgress === true) ? true : false, 
                                    { isDataLoaded: isDataLoaded, rows: (validationData.multiline ? validationData.multiline : textFieldRows),
                                    textType: (validationData.inputType ? validationData.inputType : "text"), validationData: validationData,
                                    tooltipText: validationData.tooltipText }
                                );

            if(saveAsTemplateForTable !== false && typeof saveAsTemplateForTable === 'string') {
                if(!this.templateInputsForTable[saveAsTemplateForTable]) {
                    this.templateInputsForTable[saveAsTemplateForTable] = [];
                }
                this.templateInputsForTable[saveAsTemplateForTable].push(property);

                if(!this.props.savedTemplateInputsForTable || 
                    JSON.stringify(this.templateInputsForTable) !== JSON.stringify(this.props.savedTemplateInputsForTable)
                ) {
                    this.props.setSavedTemplateInputsForTable(this.templateInputsForTable);
                }
            } else if(this.props.savedTemplateInputsForTable) {
                this.templateInputsForTable = this.props.savedTemplateInputsForTable;
            }

            if(attr.DisplayOrder < 0 || skipRender || (isFastCalculation && this.props.isFastCalculation)) { // attr hidden
                if(skipRender || (isFastCalculation && this.props.isFastCalculation)) {
                    this.deleteFromTab(attr.ProductAttributeId);
                }
                return false;
            }

            this.tempInputIdUserFieldPairs[property.id] = tempUserFields;
            
            let conditionalAttributeResult: any = this.getMemoizedConditionalAttribute(
                attr, inputsTypeValuePairs, {...inputsIdUserFieldsPairs, [property.id]: tempUserFields}, product.ProductAttributeMappings, productAttributes, gnLanguage
            );
            
            let valuesAreTheSame: boolean = false;
            if(typeof inputsTypeValuePairs[attr.Id] !== 'undefined' && typeof this.conditionalDefaultValueAlreadySetFor[attr.Id] !== 'undefined') {
                if(typeof inputsTypeValuePairs[attr.Id] !== 'object') {
                    valuesAreTheSame = inputsTypeValuePairs[attr.Id] === this.conditionalDefaultValueAlreadySetFor[attr.Id].settedValue;
                } else {
                    valuesAreTheSame = JSON.stringify(inputsTypeValuePairs[attr.Id]) === JSON.stringify(this.conditionalDefaultValueAlreadySetFor[attr.Id].settedValue);
                }
            }

            const nowMinusLastSetDefaultTimestamp: number = now - this.lastSetDefaultValueTimestamp;
            if(typeof conditionalAttributeResult.newValue !== 'undefined' && typeof conditionalAttributeResult.newValue === 'string' && 
                (!this.conditionalDefaultValueAlreadySetFor[attr.Id] || 
                    this.conditionalDefaultValueAlreadySetFor[attr.Id].value !== conditionalAttributeResult.newValue || 
                    ((nowMinusLastSetDefaultTimestamp >= 0 && nowMinusLastSetDefaultTimestamp < 50) && (conditionalAttributeResult.newValue.length > 0 && !valuesAreTheSame))
                )
            ) {
                let valueToSet: string = conditionalAttributeResult.newValue;
                let inputsToSetUpdated: boolean = false;

                if(property.type === Controls.ChoiceGroup || 
                    property.type === Controls.CheckBoxOptions
                ) {
                    if(valueToSet.length === 0) {
                        let optionsToSet: any = {};
                        attr.ProductAttributeValues.forEach((attrValue: any) => {
                            optionsToSet[attrValue.Id] = false;
                        });

                        inputsToSetUpdated = true;
                        inputsToSet.push({id: attr.Id, value: optionsToSet, userFields: tempUserFields});
                    } else {
                        let splittedValue: string[] = valueToSet.split(', ');
                        let optionsToSet: any = {};

                        let optionsAlreadySetToTrue: string[] = [];
                        splittedValue.forEach((value: string) => {
                            attr.ProductAttributeValues.forEach((attrValue: any) => {
                                if(value === attrValue.Name) {
                                    optionsToSet[attrValue.Id] = true;
                                    optionsAlreadySetToTrue.push(attrValue.Id);
                                } else if(!optionsAlreadySetToTrue.includes(attrValue.Id)) {
                                    optionsToSet[attrValue.Id] = false;
                                }
                            });
                        });

                        if(Object.keys(optionsToSet).length > 0) {
                            valueToSet = optionsToSet;
                            inputsToSetUpdated = true;
                            inputsToSet.push({id: attr.Id, value: valueToSet, userFields: tempUserFields});
                        }
                    }
                } else if(property.type === Controls.Picker) {
                    const filteredAttrValue: any = filterBySome(attr.ProductAttributeValues, 'Name', valueToSet);   

                    if(filteredAttrValue && filteredAttrValue.Id && inputsTypeValuePairs[attr.Id] !== filteredAttrValue.Id &&
                        (!this.conditionalDefaultValueAlreadySetFor[attr.Id] || this.conditionalDefaultValueAlreadySetFor[attr.Id].settedValue !== filteredAttrValue.Id)
                    ) { 
                        valueToSet = filteredAttrValue.Id;
                        inputsToSetUpdated = true;
                        inputsToSet.push({id: attr.Id, value: valueToSet, userFields: tempUserFields});
                    }
                } else if(inputsTypeValuePairs[attr.Id] !== valueToSet) {
                    inputsToSetUpdated = true;
                    inputsToSet.push({id: attr.Id, value: valueToSet, userFields: tempUserFields});
                }

                this.lastSetDefaultValueTimestamp = + new Date();

                if(inputsToSetUpdated === true) {
                    this.conditionalDefaultValueAlreadySetFor[attr.Id] = {
                        value: conditionalAttributeResult.newValue,
                        settedValue: valueToSet
                    };

                    shouldMassUpdateInputs = true;
                }
            }

            if(!!tempUserFields && !this.props.inputsIdUserFieldsPairs[attr.Id]) {
                this.props.setInputsUserFields(property.id, tempUserFields);
            }

            if(conditionalAttributeResult.show === true) {
                if(!inputsTypeValuePairs[attr.Id] || inputsTypeValuePairs[attr.Id] === '') {
                    inputsTypeValuePairs[attr.Id] = this.props.hiddenInputsTypeValuePairs[attr.Id];
                    this.props.setHiddenInputsTypeValuePairs(attr.Id, undefined); 
                }

                if(conditionalAttributeResult.disabled === true) {
                    property.disabled = true;
                }

                let errorMessage: any = !!this.props.inputErrorsText[property.id] ? this.props.inputErrorsText : undefined;

                if(tempUserFields === null || !isInTab) {
                    return renderElement(property, conditionalAttributeResult.show, iconData, this, errorMessage);
                } else {
                    this.parseTabs(tempUserFields, renderElement(property, conditionalAttributeResult.show, iconData, this, errorMessage), attr.Id, attr.ProductAttributeId, this.tabsParsed);
                    return false;
                }
            } else {
                if(this.tempInputErrors > this.prevTempInputErrors) {
                    this.tempInputErrors--;
                }

                if(!!tempUserFields) {
                    if(isInTab) {
                        this.deleteFromTab(attr.ProductAttributeId);
                    }
                    const isHideUserField: any = tempUserFields.filter((x: any) => x.Key === 'Hide');
                    if(!isHideUserField || !isHideUserField[0] || !isHideUserField[0].Value || isHideUserField[0].Value !== 'true') {
                        if(!!inputsTypeValuePairs[attr.Id]) {
                            this.props.setHiddenInputsTypeValuePairs(attr.Id, inputsTypeValuePairs[attr.Id]);
                        }
                        delete inputsTypeValuePairs[attr.Id];
                    }
                } else {
                    delete inputsTypeValuePairs[attr.Id];
                }
                delete this.conditionalDefaultValueAlreadySetFor[attr.Id];
                return false;
            }
        }) : []);

        this.savedAttributes = attributes;

        if(shouldMassUpdateInputs !== false) {
            this.setConditionalDefaultInputsData(inputsToSet);
        }

        return attributes;
    }

    render() {
        const {productAttributes, product, inputErrorsText, inputValuePairsStringified, customInputsData} = this.props;

        this.tempInputErrors = 0;

        let shouldMassUpdateInputs: boolean = false;
        let inputsToSet: any[] = [];
        
        if(product && product.UserFields && this.tabs.length === 0 && !this.tabsParsed) {
            this.setTabs(product.UserFields);
        }

        let attributes: any;
        if(Object.keys(this.savedAttributes).length === 0 || this.prevInputValuePairsStringified !== inputValuePairsStringified || 
            this.prevInputErrorsTextStringified !== JSON.stringify(inputErrorsText) || JSON.stringify(customInputsData) !== this.prevCustomInputsDataStringified ||
            this.prevAsyncActionInprogress !== this.props.asyncActionInProgress
        ) {
            this.tabs.forEach((tab: any, tabIndex: number) => {
                this.tabs[tabIndex].tabErrorsCount = 0;
            });
            
            attributes = this.mapAttributes();
        } else {
            attributes = this.savedAttributes;
        }

        this.prevInputValuePairsStringified = inputValuePairsStringified;
        this.prevInputErrorsTextStringified = JSON.stringify(inputErrorsText);
        this.prevCustomInputsDataStringified = JSON.stringify(customInputsData);
        this.prevAsyncActionInprogress = this.props.asyncActionInProgress;

        attributes = attributes.filter((x: any) => x !== false);

        if(productAttributes && !this.tabsParsed) {
            this.tabsParsed = true;
        }
        
        if(this.props.inputErrors !== this.tempInputErrors) {
            this.props.setInputErrors(this.tempInputErrors);
        }

        let inputsToMassChange: any[] = [];
        let tabs = this.tabs.map((tab: any, tabIndex: number) => {
            tab.items = tab.items.slice(0).sort(this.sortByOrder);
            
            let tabItems: any[] = [];
            tab.items.forEach((item: any) => {
                if(!item.hide) {
                    tabItems.push(<div className={classNames.myTabItem} key={item.key}>{item.value}</div>);
                } else if(this.props.isEditMode && this.tabInputsInEditModeSet === false) {
                    inputsToMassChange.push(item);
                }
            });

            if(tabItems.length > 0) {
                return <PivotItem headerText={tab.name} key={tab.key + tab.items.length}>
                    <FocusZone direction={FocusZoneDirection.horizontal} handleTabKey={FocusZoneTabbableElements.all} isCircularNavigation={true}>
                        {tab.name === 'APK' && 
                            <LabelContainerComponent>
                                {(this.props.product.SeName === "ubezpieczenie-auta" || this.props.product.SeName === "transport-pow-35t-dmc") && (
                                    <>
                                        <LabelComponent label={L("Registration number")} required={true} />
                                        <ComboBox
                                            label={''}
                                            text={this.props.inputsTypeValuePairs['registrationNumber']}
                                            required={true}
                                            allowFreeform={true}
                                            autoComplete={'on'}
                                            options={this.props.vehicleOptions.dropdown}
                                            className={`${classNames.comboBoxStyles}`}
                                            key={`RegistrationNumberComboBox`}
                                            onChange={(event: React.FormEvent<IComboBox>, option?: IComboBoxOption, index?: number, value?: string) => {
                                                let tempValue: string = "";

                                                if (option && !!option.key && !!option.text) {
                                                    tempValue = typeof option.key === 'number' ? option.key.toString() : option.key;
                                                } else if (typeof value !== 'undefined') {
                                                    const splitedValue = value.split(' | ');
                                                    tempValue = splitedValue[0];
                                                }
                                                this.props.onInputChange('registrationNumber', tempValue, undefined);
                                                this.forceUpdate();
                                            }}
                                            onPendingValueChanged={(option?: IComboBoxOption, index?: number, value?: string) => {
                                                if (typeof value !== 'undefined') {
                                                    const splitedValue = value.split(' | ');
                                                    this.props.onInputChange('registrationNumber', splitedValue[0], undefined);
                                                    this.forceUpdate();
                                                }
                                            }} 
                                        />
                                    </>
                                )}
                            </LabelContainerComponent>
                        }
                        {tabItems}
                    </FocusZone>
                </PivotItem>;
            } else {
                return false;
            }
        });

        if(this.tabInputsInEditModeSet === false) {
            this.tabInputsInEditModeSet = true;
            
            const cloneInputsTypeValuePairs = {};
            const cloneInputsIdUserFieldsPairs = {};

            inputsToMassChange.forEach((element) => {
                if(!!element.value.props.value) {
                    cloneInputsTypeValuePairs[element.value.key] = !!element.value.props.value ? 
                                                            (isJsonString(element.value.props.value) ? 
                                                                JSON.parse(element.value.props.value) : element.value.props.value) 
                                                            : '';
                    cloneInputsIdUserFieldsPairs[element.value.key] = this.tempInputIdUserFieldPairs[element.value.key];
                }
            });

            this.props.onMassInputChange(cloneInputsTypeValuePairs, cloneInputsIdUserFieldsPairs);
        }

        if(shouldMassUpdateInputs !== false) {
            this.setConditionalDefaultInputsData(inputsToSet);
        }

        this.lastRenderTimestamp = + new Date();

        const pivotStyles = {
            root: {
                marginLeft: '-8px'
            },
            linkIsSelected: {
                color: myTheme.palette.red,
                selectors: {
                    ':before': {
                        height: '5px',
                        backgroundColor: additionalTheme.darkerRed,
                    }
                }
            }
        };
        return <>
            <div style={{display: 'flex', flexDirection: 'row', 'alignItems': 'center'}}>
                {this.props.asyncActionInProgress ? <Spinner label={L('Filling in data...')} className={classNames.loadSpinnerTopBar} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" /> : ''}
            </div>

            <Pivot className={`${this.tabs.length > 0 ? '' : classNames.toolbar}`} theme={myTheme} styles={pivotStyles}>
                {this.tabs.length > 0 ?
                    tabs :
                    <PivotItem>
                        {attributes}
                    </PivotItem>
                }
            </Pivot>
        </>;
    }

    private getMemoizedConditionalAttribute(attr: any, inputsTypeValuePairs: any, idUserFieldsPairs: any, mappings: any, items: any, language: any): any {
        const attrId: string = attr.Id;
        
        // Determine which inputs affect this attribute
        if (!this.inputDependencies.has(attrId)) {
            const analyzeResult: {dependenciesIds: Set<string>, tempKeyIdPairs: Map<string, string>} = analyzeAttributeDependencies(
                attrId, attr, this.keyIdPairs, this.tempInputIdUserFieldPairs[attrId], mappings, this.props.product
            );

            this.inputDependencies.set(attrId, analyzeResult.dependenciesIds);
            this.keyIdPairs = new Map(analyzeResult.tempKeyIdPairs);
        }
        
        // Get the relevant inputs for this attribute
        const relevantInputs = this.inputDependencies.get(attrId) || new Set(Object.keys(inputsTypeValuePairs));
        
        // Create a dependency key from only the relevant inputs
        const inputsStateKey: string = createInputsStateKey(Array.from(relevantInputs), inputsTypeValuePairs);
        
        // Check cache
        const cacheKey: string = `${attrId}_${inputsStateKey}`;
        if (this.conditionalAttributeCache.has(cacheKey)) {
            return this.conditionalAttributeCache.get(cacheKey);
        }
        
        // Calculate new result
        const result = conditionalAttribute(attr, inputsTypeValuePairs, idUserFieldsPairs, mappings, items, language);
        
        // Cache the result
        this.conditionalAttributeCache.set(cacheKey, result);
        
        return result;
    }
}