import { VehicleConfigDto } from '../services/vehicleConfig/vehicleConfigDto';
import VehicleConfigService from '../services/vehicleConfig/vehicleConfigService';
import { defaultClient } from './clientStore';
import { CrudStoreBase } from './crudStoreBase';

class VehicleStore extends CrudStoreBase<VehicleConfigDto>{
	constructor() {
		super(VehicleConfigService, defaultVehicle)
	}

	public async getByClientId(clientId: string) {
		await VehicleConfigService.getByClientId(clientId).then((result: any) => {
			this.dataSet = {
				totalCount: result.items.length, 
				items: result.items
			};
		}).catch((error: any) => {
			console.error(error);
		});
	}
}

export const defaultVehicle = {
	id: '',
	clientId: 0,
	client: defaultClient,
	vin: '',
	firstRegistrationDate: '',
	registrationNumber: '',
	mileage: 0,
	type: '',
	brand: '',
	year: '',
	fuelType: '',
	engineCapacity: '',
	model: '',
	enginePower: '',
	eurotaxCarId: '',
	infoExpertId: '',
	vehicleInfo: '',
	lastModificationTime: '',
	lastModifierUserId: 0,
	creationTime: '',
	creatorUserId: 0,
	dmc: 0,
	netWeight: 0,
	capacity: 0,
}

export default VehicleStore;