import * as React from 'react';
import { Redirect, Switch, Route } from 'react-router-dom';
import DocumentTitle from 'react-document-title';
import Header from '../../components/Header';
import ProtectedRoute from '../../components/Router/ProtectedRoute';
import SiderMenu from '../../components/SiderMenu';
import { appRouters } from '../Router/router.config';
import utils from '../../utils/utils';
import NotFoundRoute from '../Router/NotFoundRoute';
import { inject } from 'mobx-react';
import Stores from '../../stores/storeIdentifier';
import { mergeStyleSets } from '@fluentui/merge-styles';
import {additionalTheme, myTheme} from '../../styles/theme';
import ChatContainer from '../Chat/ChatContainer';
import AppConsts from '../../lib/appconst';

const classNames = mergeStyleSets({
  layout: {
    height: '100vh',
    backgroundColor: additionalTheme.darkerWhite,
  },
  headerWrapper: {
    backgroundColor: myTheme.palette.white,
    marginBottom: '40px',
    borderRadius: '12px',
    boxShadow: '0px 1px 2px 0px rgba(0, 0, 0, 0.10)',

  },
  contentWrapper: {
    display: 'flex',
    height: 'calc(100vh - 170px)',
    flexDirection: 'row',
  },
  customContent: {
    margin: 0,
    position: 'relative',
    minHeight: 'unset',
    height: '84vh',
    overflowY: 'auto',
    overflowX: 'hidden',
  },
  contentDashboard: {
    margin: 0,
    position: 'relative',
    minHeight: 'unset',
    height: '84vh',
    overflowY: 'auto',
    overflowX: 'hidden',
  },
  mainWrapper: {
    display: 'flex',
    flex: 'auto',
    flexDirection: 'column',
    overflowX: 'hidden',
  },
});

@inject(Stores.SearchStore, Stores.UserStore)
class AppLayout extends React.Component<any> {
  private prevPathname: string = '';

  state = {
    collapsed: false,
  };

  toggle = () => {
    this.setState({
      collapsed: !this.state.collapsed,
    });
  };

  onCollapse = (collapsed: any) => {
    this.setState({ collapsed });
  };

  render() {
    const {
      history,
      location: { pathname },
      searchStore,
      userStore,
    } = this.props;

    const { path } = this.props.match;
    const { collapsed } = this.state;

    if(pathname !== this.prevPathname) {
      this.prevPathname = pathname;
      this.props.searchStore.setText('');
    }

    const contentDashboard = (
      <div className={classNames.contentWrapper}>
        <SiderMenu path={path} toggle={this.toggle} history={history} collapsed={collapsed} />
        <div className={classNames.mainWrapper}>
          <div className={classNames.contentDashboard}>
            <Switch>
              {pathname === '/' && <Redirect from="/" to="/customer-list" />}
              {appRouters
                .filter((item: any) => !item.isLayout)
                .map((route: any, index: any) => (
                  <Route
                    exact
                    key={index}
                    path={route.path}
                    render={(props) => <ProtectedRoute component={route.component} permission={route.permission} />}
                  />
                ))}
              {pathname !== '/' && <NotFoundRoute />}
            </Switch>
          </div>
        </div>
      </div>
    );

    const contentWithSideMenu = (
      <div className={classNames.contentWrapper}>
        <SiderMenu path={path} toggle={this.toggle} history={history} collapsed={collapsed} />
        <div className={classNames.mainWrapper}>
          <div className={classNames.customContent}>
            <Switch>
              {pathname === '/' && <Redirect from="/" to="/customer-list" />}
              {appRouters
                .filter((item: any) => !item.isLayout)
                .map((route: any, index: any) => (
                  <Route
                    exact
                    key={index}
                    path={route.path}
                    render={(props) => <ProtectedRoute component={route.component} permission={route.permission} />}
                  />
                ))}
              {pathname !== '/' && <NotFoundRoute />}
            </Switch>
          </div>
        </div>
      </div>
    );

    const layout = (
      <div className={classNames.layout}>
        <div className={classNames.headerWrapper}>
          <Header collapsed={this.state.collapsed} toggle={this.toggle} searchStore={searchStore} userStore={userStore} pathname={pathname} />
        </div>
        {pathname === '/' || pathname === '/customer-list' ? contentDashboard : contentWithSideMenu}
        {/* <Footer /> */}
      </div>
    );
    
    return <>
      <DocumentTitle title={utils.getPageTitle(pathname)}>
        {layout}
      </DocumentTitle>

      {(!!AppConsts && (typeof AppConsts.disableChat === 'undefined' || AppConsts.disableChat === false)) &&
        <ChatContainer />
      }
    </>;
  }
}

export default AppLayout;
