import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { L } from '../../../lib/abpUtility';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { ICrudPermissons } from "../../BaseComponents/commandBarBase";
import { RouterPath } from "../../../components/Router/router.config";
import { Link } from "@fluentui/react";
import { VehicleDto } from "../../../services/vehicle/vehicleDto";
import { VehiclePanel } from "./vehiclePanel";

export class VehicleTable extends FluentTableBase<VehicleDto> {
  getItemDisplayNameOf(item: VehicleDto): string {
    return item.registrationNumber || '';
  }

  getColumns(): ITableColumn[] {
    return VehicleTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        name: L('ID'),
        fieldName: 'id',
        minWidth: 30,
        maxWidth: 30,
        onRender: (item: any): any => {
          return <Link onClick={(e) => {
                        e.preventDefault();
                        props.history.push(`/${RouterPath.Vehicle}/${item.id}`);
                      }}
                        href={`/${RouterPath.Vehicle}/${item.id}`}>
                  {item.id || ''}
                </Link>
        }
      },
      {
        name: L('Owner'),
        fieldName: 'client.user.fullName',
        minWidth: 170,
        maxWidth: 170,
        onRender: (item: any): any => {
          if(item && item.client && item.client.user) {
            return item.client.user.fullName;
          }
          return '-';
        }
      },
      // Commented out until coowner and user properties are implemented in VehicleDto
      // {
      //   name: L('Coowner'),
      //   fieldName: 'coowner.user.fullName',
      //   minWidth: 170,
      //   maxWidth: 170,
      //   onRender: (item: any): any => {
      //     if(item && item.coowner && item.coowner.user) {
      //       return item.coowner.user.fullName;
      //     }
      //     return '-';
      //   }
      // },
      // {
      //   name: L('User'),
      //   fieldName: 'user.user.fullName',
      //   minWidth: 170,
      //   maxWidth: 170,
      //   onRender: (item: any): any => {
      //     if(item && item.user && item.user.user) {
      //       return item.user.user.fullName;
      //     }
      //     return '-';
      //   }
      // },
      {
        name: L('VIN number'),
        fieldName: 'vin',
        minWidth: 140,
        maxWidth: 140,
      },
      {
        name: L('Registration number'),
        fieldName: 'registrationNumber',
        minWidth: 140,
        maxWidth: 140,
      },
      {
        name: L('Production year'),
        fieldName: 'year',
        minWidth: 120,
        maxWidth: 120,
      },
      {
        name: L('Mileage'),
        fieldName: 'mileage',
        minWidth: 70,
        maxWidth: 70,
      },
      {
        name: L('Eurotax ID'),
        fieldName: 'eurotaxCarId',
        minWidth: 120,
        maxWidth: 120,
      },
      {
        name: L('Infoexpert ID'),
        fieldName: 'infoExpertId',
        minWidth: 120,
        maxWidth: 120,
      },
      {
        name: L('Vehicle type'),
        fieldName: 'type',
        minWidth: 120,
        maxWidth: 120,
        onRender: (item: any): any => {
          return L(item.type) || '';
        }
      },
      {
        name: L('Fuel type'),
        fieldName: 'fuelType',
        minWidth: 120,
        maxWidth: 120,
        onRender: (item: any): any => {
          return L(item.fuelType) || '';
        }
      },
      {
        name: L('Vehicle info'),
        fieldName: 'vehicleInfo',
        minWidth: 120,
        maxWidth: 120,
      },
    ];
  }

  getTitle(): string {
    return L('Vehicles');
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: true,
      update: true,
      delete: true,
      customActions: false,
    };
  }

  transformToDisplayValues(row: VehicleDto): any {
    // Ensure all properties are properly formatted to prevent React rendering errors
    const safeRow = {
      ...row,
      id: row.id || '',
      registrationNumber: row.registrationNumber || '',
      vin: row.vin || '',
      type: row.type || '',
      fuelType: row.fuelType || '',
      brand: row.brand || '',
      model: row.model || '',
      year: row.year || '',
      mileage: row.mileage || 0,
      eurotaxCarId: row.eurotaxCarId || '',
      infoExpertId: row.infoExpertId || '',
      vehicleInfo: row.vehicleInfo || '',
      // Ensure client object exists
      client: row.client || null
    };

    console.log('VehicleTable transformToDisplayValues:', safeRow);
    return safeRow;
  }

  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <>
      <VehiclePanel
        {...props}
      />
    </>
  }
}