import { Default<PERSON><PERSON>on, Dialog, DialogFooter, DialogType, Icon, IDropdownOption, Link, mergeStyleSets, Pivot, PivotItem, PrimaryButton, Selection, SelectionMode, Spinner, SpinnerSize, Stack } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import {additionalTheme, myTheme} from '../../../styles/theme';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import Stores from '../../../stores/storeIdentifier';
import { inject } from 'mobx-react';
import { ContentViewModelProperty } from '../../BaseComponents/contentViewBase';
import { Controls } from '../../BaseComponents/controls';
import { defaultVehicle } from '../../../stores/vehicleStore';
import policyDictionaryService from '../../../services/policyDictionary/policyDictionaryService';
import { RouterPath } from '../../../components/Router/router.config';
import { EurotaxExpertInfoMixedBox } from './eurotaxExpertInfoMixedBox';
import { CustomerFluentListBase } from '../../BaseComponents/customerFluentListBase';
import { VehicleDto } from '../../../services/vehicle/vehicleDto';
import { fluentTableClassNames } from '../../../styles/fluentTableStyles';
import vehicleConfigService from '../../../services/vehicleConfig/vehicleConfigService';
import { CheckBoxBase } from '../../BaseComponents/CheckBoxBase';

const classNames = mergeStyleSets({
    inputIcon: {
        cursor: 'pointer',
        marginLeft: '15px !important',
        marginRight: '10px',
        fontSize: '20px',
        marginTop: '26px',
        transition: 'all 120ms',
        selectors: {
            '&:hover': {
                transform: 'scale(1.2)',
            }
        }
    },
    smallLoadSpinner: {
        display: 'inline-flex',
        marginLeft: '10px !important',
        marginTop: '22px',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
});

@inject(Stores.LanguageStore)
@inject(Stores.VehicleStore)
@inject(Stores.ClientStore)
export class VehicleContentView extends GenericContentView {
    private vehicle: VehicleDto = defaultVehicle;
    private vehicleTypesOptions = {
        dropdown: [] as IDropdownOption[]
    };
    private fuelTypesOptions = {
        dropdown: [] as IDropdownOption[]
    };
    private showVehicleDialog: boolean = false;
    private collapseEurotaxInfoexpertForm: boolean = false;
    private existingVehicleId: number = -1;
    private customInputsData: any = {
        vehicleBrandId: "",
        vehicleModelId: "",
        engineCapacity: "",
        enginePower: 0,
        vehicleConfigurationEurotaxId: "",
        vehicleConfigurationInfoExpertId: "",
        vehicleInfo: "",
    };
    private selectClientSearchText: string = "";
    private _clientListSelection: Selection = new Selection({
        onSelectionChanged: () => {
            const selectedClient: any = this._clientListSelection.getSelection();
            if(Array.isArray(selectedClient) && selectedClient.length > 0 && !!selectedClient[0].id) {
                this.vehicle.clientId = selectedClient[0].id;
                this.vehicle.client = selectedClient[0];
                this._clientListSelection.setAllSelected(false);
            }
            this.forceUpdate();
        },
        selectionMode: SelectionMode.single
    });
    private showVehicleCoownerInput: boolean = false;
    private showVehicleUserInput: boolean = false;

    async componentDidMount() {
        await this.props.store?.getAll(this.props.store?.defaultRequest);
        // await this.props.clientStore?.getAll({...this.props.clientStore?.defaultRequest, maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE});

        this.checkIfDataIsLoaded("vehicle");

        await policyDictionaryService.getVehicleTypes().then((response: any) => {
            if(response.data && response.data.result) {
                this.vehicleTypesOptions.dropdown = response.data.result.map((type: any) => {
                    return { key: type, 
                            text: L(type), 
                            isSelected: type === this.vehicle.type };
                }) as IDropdownOption[];
            }
        }).catch((error: any) => {
            console.error(error);
        });

        await policyDictionaryService.getFuelTypes().then((response: any) => {
            if(response.data && response.data.result) {
                this.fuelTypesOptions.dropdown = response.data.result.map((type: any) => {
                    return { key: type, 
                            text: L(type), 
                            isSelected: type === this.vehicle.fuelType };
                }) as IDropdownOption[];
            }
        }).catch((error: any) => {
            console.error(error);
        });

        this.customInputsData = {
            vehicleBrandId: this.vehicle.brand,
            vehicleModelId: this.vehicle.vehicleModel,
            engineCapacity: !!this.vehicle.engineCapacity ? parseInt(this.vehicle.engineCapacity) : '',
            enginePower: !!this.vehicle.enginePower ? parseInt(this.vehicle.enginePower) : 0,
            vehicleConfigurationEurotaxId: this.vehicle.eurotaxCarId,
            vehicleConfigurationInfoExpertId: this.vehicle.infoExpertId,
            vehicleInfo: this.vehicle.vehicleInfo,
        };

        this.forceUpdate();
    }

    private async getVehicleByVin(vin: string) {
        if(!!vin && vin.length === 17) {
            this.asyncActionInProgress = true;
            this.forceUpdate();

            await vehicleConfigService.getByVin(vin).then((response: any) => {
                if(response && response.id) {
                    this.existingVehicleId = response.id;
                    this.showVehicleDialog = true;
                }
            });
            
            this.asyncActionInProgress = false;
            this.forceUpdate();
        }
    }

    renderContent() {
        this.vehicle = this.props.payload.model ? this.props.payload.model : this.props.payload;

        if(!this.vehicle.id || this.vehicle.id.length === 0) {
            this.vehicle.id = '0';
        }

        if(!!this.vehicle.dmc && !!this.vehicle.netWeight && (!this.vehicle.capacity || this.vehicle.capacity !== (this.vehicle.dmc - this.vehicle.netWeight))) {
            this.vehicle.capacity = this.vehicle.dmc - this.vehicle.netWeight;
        }

        const pivotStyles = {
            root: {
                marginLeft: '-8px'
            },
            linkIsSelected: {
                color: myTheme.palette.red,
                selectors: {
                    ':before': {
                        height: '5px',
                        backgroundColor: additionalTheme.darkerRed
                    }
                }
            }
        };

        return <>
            <Dialog
                hidden={!this.showVehicleDialog}
                onDismiss={() => { this.showVehicleDialog = false; this.forceUpdate(); }}
                dialogContentProps={{
                    type: DialogType.normal,
                    title: L("Vehicle with this VIN already exist. Do you want to go to existing vehicle?"),
                    closeButtonAriaLabel: L('Close'),
                }}
                modalProps={{
                    // titleAriaId: this._labelId,
                    // subtitleAriaId: this._subTextId,
                    isBlocking: true,
                    styles: { main: { maxWidth: 450 } },
                }}
                theme={myTheme}
            >
                <DialogFooter theme={myTheme}>
                    <DefaultButton theme={myTheme} onClick={() => { this.showVehicleDialog = false; this.forceUpdate(); }} text={L('No')} />
                    <Link theme={myTheme} href={`/${RouterPath.Vehicle}/${this.existingVehicleId}`}>
                        <PrimaryButton
                            text={L('Yes')}
                            theme={myTheme}
                            disabled={false}
                        />
                    </Link>
                </DialogFooter>
            </Dialog>
            
            <Pivot theme={myTheme} styles={pivotStyles}>
                <PivotItem headerText={L('General')} key={'General'}>
                    <div className={fluentTableClassNames.contentContainer}>
                        <CustomerFluentListBase
                            searchText={this.selectClientSearchText}
                            items={this.props.clientStore?.dataSet && this.props.clientStore?.dataSet.items ? this.props.clientStore?.dataSet.items : []}
                            store={this.props.clientStore!}
                            history={this.props.history}
                            scrollablePanelMarginTop={70}
                            customData={{
                                selectedClient: this.vehicle.clientId && this.vehicle.client ? `[${this.vehicle.clientId}] ${this.vehicle.client && this.vehicle.client.user && this.vehicle.client.user.fullName ? this.vehicle.client.user.fullName : '-'}` : undefined,
                                customLabel: L('Select client (vehicle owner)')
                            }}
                            customOnSearchTextChanged={(text: string) => { this.selectClientSearchText = text; this.forceUpdate(); }}
                            customSelection={this._clientListSelection}
                            customOnSelectionChanged={(selection: any) => {
                                if(typeof selection === 'string' && selection === 'deleteClient') {
                                    this.vehicle.clientId = defaultVehicle.clientId;
                                    this.vehicle.client = defaultVehicle.client;
                                    this.forceUpdate();
                                }
                            }}
                        />
                    </div>

                    <>
                        <CheckBoxBase label={L("Vehicle has coowner")} 
                            value={this.showVehicleCoownerInput} disabled={!this.isDataLoaded}
                            onChange={(e: any) => {
                                this.showVehicleCoownerInput = !this.showVehicleCoownerInput;
                                this.forceUpdate();
                            }}
                        />

                        {this.showVehicleCoownerInput &&
                            <div className={fluentTableClassNames.contentContainer}>
                                <CustomerFluentListBase
                                    searchText={this.selectClientSearchText}
                                    items={this.props.clientStore?.dataSet && this.props.clientStore?.dataSet.items ? this.props.clientStore?.dataSet.items : []}
                                    store={this.props.clientStore!}
                                    history={this.props.history}
                                    scrollablePanelMarginTop={70}
                                    customData={{
                                        // selectedClient: this.vehicle.clientId && this.vehicle.client ? `[${this.vehicle.clientId}] ${this.vehicle.client && this.vehicle.client.user && this.vehicle.client.user.fullName ? this.vehicle.client.user.fullName : '-'}` : undefined,
                                        customLabel: L('Select vehicle coowner')
                                    }}
                                    customOnSearchTextChanged={(text: string) => { this.selectClientSearchText = text; this.forceUpdate(); }}
                                    customSelection={this._clientListSelection}
                                    customOnSelectionChanged={(selection: any) => {
                                        if(typeof selection === 'string' && selection === 'deleteClient') {
                                            // this.vehicle.clientId = defaultVehicle.clientId;
                                            // this.vehicle.client = defaultVehicle.client;
                                            this.forceUpdate();
                                        }
                                    }}
                                />
                            </div>
                        }    
                    </>

                    <>
                        <CheckBoxBase label={L("Vehicle has user")} 
                                value={this.showVehicleUserInput} disabled={!this.isDataLoaded}
                                onChange={(e: any) => {
                                    this.showVehicleUserInput = !this.showVehicleUserInput;
                                    this.forceUpdate();
                                }}
                        />

                        {this.showVehicleUserInput &&
                            <div className={fluentTableClassNames.contentContainer}>
                                <CustomerFluentListBase
                                    searchText={this.selectClientSearchText}
                                    items={this.props.clientStore?.dataSet && this.props.clientStore?.dataSet.items ? this.props.clientStore?.dataSet.items : []}
                                    store={this.props.clientStore!}
                                    history={this.props.history}
                                    scrollablePanelMarginTop={70}
                                    customData={{
                                        // selectedClient: this.vehicle.clientId && this.vehicle.client ? `[${this.vehicle.clientId}] ${this.vehicle.client && this.vehicle.client.user && this.vehicle.client.user.fullName ? this.vehicle.client.user.fullName : '-'}` : undefined,
                                        customLabel: L('Select vehicle user')
                                    }}
                                    customOnSearchTextChanged={(text: string) => { this.selectClientSearchText = text; this.forceUpdate(); }}
                                    customSelection={this._clientListSelection}
                                    customOnSelectionChanged={(selection: any) => {
                                        if(typeof selection === 'string' && selection === 'deleteClient') {
                                            // this.vehicle.clientId = defaultVehicle.clientId;
                                            // this.vehicle.client = defaultVehicle.client;
                                            this.forceUpdate();
                                        }
                                    }}
                                />
                            </div>
                        }
                    </>

                    {this.renderElement(new ContentViewModelProperty('registrationNumber', L("Registration number"), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, validationData: {
                        mask: "********", maskFormat: {'*': /[A-Z0-9-]/}, maskChar: ""
                    }}), [], {'registrationNumber': this.vehicle.registrationNumber})}
                    
                    <Stack horizontal={true}>
                        {this.renderElement(new ContentViewModelProperty('vin', L("VIN"), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, validationData: {
                            mask: "*****************", maskChar: "", maxlength: 17, minlength: 17
                        }}), [], {'vin': this.vehicle.vin ? this.vehicle.vin.trim() : ''})}
                        <Icon iconName='SearchAndApps' className={classNames.inputIcon} onClick={() => this.getVehicleByVin(this.vehicle.vin ? this.vehicle.vin.trim() : '') } title={L('Check if vehicle with this VIN already exist')} />
                    
                        {this.asyncActionInProgress &&
                            <Spinner label={L("Searching vehicle")} className={classNames.smallLoadSpinner} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" />
                        }
                    </Stack>

                    {/* {this.renderElement(new ContentViewModelProperty('vin', L("VIN"), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded}), [], {'vin': this.vehicle.vin})} */}
                    {this.renderElement(new ContentViewModelProperty('mileage', L("Mileage"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, textType: "number", min: '0'}), [], {'mileage': this.vehicle.mileage})}
                    {this.renderElement(new ContentViewModelProperty('firstRegistrationDate', L("First registration date"), Controls.Date, true, [], false, {isDataLoaded: this.isDataLoaded}), [], {'firstRegistrationDate': this.vehicle.firstRegistrationDate})}
                    {/* {this.renderElement(new ContentViewModelProperty('productionYear', L("Production year"), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, textType: "number", min: '1922', max: new Date().getFullYear()}), [], {'productionYear': this.vehicle.productionYear})} */}
                    {/* {this.renderElement(new ContentViewModelProperty('vehicleType', L("Vehicle type"), Controls.Picker, true, this.vehicleTypesOptions, false, {isDataLoaded: this.isDataLoaded}), [], {'vehicleType': this.vehicle.vehicleType})} */}
                    {/* {this.renderElement(new ContentViewModelProperty('fuelType', L("Fuel type"), Controls.Picker, true, this.fuelTypesOptions, false, {isDataLoaded: this.isDataLoaded}), [], {'fuelType': this.vehicle.fuelType})} */}
                    
                    <EurotaxExpertInfoMixedBox 
                        collapseForm={this.collapseEurotaxInfoexpertForm}
                        onFormCollapse={() => { this.collapseEurotaxInfoexpertForm = !this.collapseEurotaxInfoexpertForm; this.forceUpdate(); }}
                        customInputsData={{
                            vehicleTypeId: this.vehicle.type,
                            vehicleBrandId: this.customInputsData.vehicleBrandId,
                            productionYear: this.vehicle.year,
                            fuelType: this.vehicle.fuelType,
                            vehicleModelId: this.customInputsData.vehicleModelId,
                            engineCapacity: this.customInputsData.engineCapacity,
                            enginePower: this.customInputsData.enginePower,
                            vehicleConfigurationEurotaxId: this.customInputsData.vehicleConfigurationEurotaxId,
                            vehicleConfigurationInfoExpertId: this.customInputsData.vehicleConfigurationInfoExpertId,
                            vehicleInfo: this.customInputsData.vehicleInfo,
                        }}
                        onInputChange={ (id: string, value: any, customInputData: boolean | undefined) => {
                            let idMappedForVehicle: string = id;
                            
                            switch(id) {
                                case 'vehicleType':
                                    idMappedForVehicle = 'type';

                                    this.customInputsData['vehicleBrandId'] = '';
                                    this.vehicle.year = defaultVehicle.year;
                                    this.vehicle.fuelType = defaultVehicle.fuelType;
                                    this.customInputsData['vehicleModelId'] = '';
                                    this.customInputsData['vehicleConfigurationId'] = '';
                                    this.customInputsData['vehicleConfigurationEurotaxId'] = '';
                                    this.customInputsData['vehicleConfigurationInfoExpertId'] = '';
                                    this.customInputsData['enginePower'] = 0;
                                    this.customInputsData['engineCapacity'] = '';
                                    this.customInputsData['vehicleInfo'] = '';
                                break;
                        
                                case 'vehicleBrandId':
                                    idMappedForVehicle = 'brand';

                                    if(!!this.customInputsData['vehicleBrandId']) {
                                        this.vehicle.year = defaultVehicle.year;
                                    }
                                    
                                    this.vehicle.year = defaultVehicle.year;
                                    this.vehicle.fuelType = defaultVehicle.fuelType;
                                    this.customInputsData['vehicleModelId'] = '';
                                    this.customInputsData['vehicleConfigurationId'] = '';
                                    this.customInputsData['vehicleConfigurationEurotaxId'] = '';
                                    this.customInputsData['vehicleConfigurationInfoExpertId'] = '';
                                    this.customInputsData['enginePower'] = 0;
                                    this.customInputsData['engineCapacity'] = '';
                                    this.customInputsData['vehicleInfo'] = '';
                                break;
                        
                                case 'productionYear':
                                    idMappedForVehicle = 'year';

                                    this.vehicle.fuelType = defaultVehicle.fuelType;
                                    this.customInputsData['vehicleModelId'] = '';
                                    this.customInputsData['vehicleConfigurationId'] = '';
                                    this.customInputsData['vehicleConfigurationEurotaxId'] = '';
                                    this.customInputsData['vehicleConfigurationInfoExpertId'] = '';
                                    this.customInputsData['enginePower'] = 0;
                                    this.customInputsData['engineCapacity'] = '';
                                    this.customInputsData['vehicleInfo'] = this.customInputsData['vehicleBrandId'];
                                    if (this.customInputsData['vehicleBrandId'] !== '' && this.customInputsData['vehicleBrandId'] !== undefined) {
                                        this.vehicle.vehicleInfo = this.customInputsData['vehicleBrandId'];
                                    }
                        
                                    // this.inputsTypeValuePairs[this.mapAttributeNameToId('productionYear')] = value;
                                break;

                                case 'fuelType':
                                    this.customInputsData['vehicleConfigurationId'] = '';
                                    this.customInputsData['vehicleConfigurationEurotaxId'] = '';
                                    this.customInputsData['vehicleConfigurationInfoExpertId'] = '';
                                    this.customInputsData['vehicleModelId'] = '';
                                    this.customInputsData['engineCapacity'] = '';
                                    this.customInputsData['enginePower'] = 0;
                                    this.customInputsData['vehicleInfo'] = '';
                                break;
                        
                                case 'engineCapacity':
                                    this.customInputsData['vehicleConfigurationId'] = '';
                                    this.customInputsData['vehicleConfigurationEurotaxId'] = '';
                                    this.customInputsData['vehicleConfigurationInfoExpertId'] = '';
                                    this.customInputsData['vehicleModelId'] = '';
                                    this.customInputsData['enginePower'] = 0;
                                break;

                                case 'vehicleModelId':
                                    idMappedForVehicle = 'vehicleModel';

                                    this.customInputsData['vehicleConfigurationId'] = '';
                                    this.customInputsData['vehicleConfigurationEurotaxId'] = '';
                                    this.customInputsData['vehicleConfigurationInfoExpertId'] = '';
                                    this.customInputsData['enginePower'] = 0;
                                break;
                        
                                case 'enginePower':
                                    this.customInputsData['vehicleConfigurationId'] = '';
                                    this.customInputsData['vehicleConfigurationEurotaxId'] = '';
                                    this.customInputsData['vehicleConfigurationInfoExpertId'] = '';
                                    this.customInputsData['vehicleInfo'] = `${this.customInputsData['vehicleBrandId']}, ${this.customInputsData['vehicleModelId']}`;
                                    if (this.customInputsData['vehicleBrandId'] !== '' && this.customInputsData['vehicleBrandId'] !== undefined) {
                                        this.vehicle.vehicleInfo = `${this.customInputsData['vehicleBrandId']}, ${this.customInputsData['vehicleModelId']}`;
                                    }
                                break;

                                case 'dmc':
                                    if(!!value) {
                                        this.vehicle.dmc = value;
                                    }
                                break;
                            }
                            
                            if(customInputData === true) {
                                this.customInputsData[id] = value;
                            }
                            
                            this.vehicle[idMappedForVehicle] = value;
                            
                            this.forceUpdate();
                        }}
                    />

                    {this.renderElement(new ContentViewModelProperty('eurotaxCarId', L("Eurotax"), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'eurotaxCarId': this.vehicle.eurotaxCarId})}
                    {this.renderElement(new ContentViewModelProperty('infoExpertId', L("Infoexpert"), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'infoExpertId': this.vehicle.infoExpertId})}
                    {this.renderElement(new ContentViewModelProperty('dmc', L("DMC"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'dmc': this.vehicle.dmc})}
                    {this.renderElement(new ContentViewModelProperty('netWeight', L("Masa własna"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'netWeight': this.vehicle.netWeight})}
                    {this.renderElement(new ContentViewModelProperty('capacity', L("Ładowność"), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'capacity': this.vehicle.capacity})}
                    {this.renderElement(new ContentViewModelProperty('vehicleInfo', L("Vehicle info"), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded}), [], {'vehicleInfo': this.vehicle.vehicleInfo})}
                </PivotItem>
            </Pivot>
        </>;
    }
}