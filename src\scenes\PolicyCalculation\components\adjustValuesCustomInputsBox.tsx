import { Constrain<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alog<PERSON>ooter, DialogType, FocusZone, FocusZoneDirection, FocusZoneTabbableElements, IChoiceGroupOption, IDetailsColumnStyles, IDropdownOption, IStackStyles, IStackTokens, MarqueeSelection, mergeStyleSets, MessageBar, PrimaryButton, ScrollablePane, ScrollbarVisibility, Selection, SelectionMode, ShimmeredDetailsList, Spinner, SpinnerSize, Stack } from "@fluentui/react";
import { inject, observer } from "mobx-react";
import React from "react";
import { L } from "../../../lib/abpUtility";
import AppConsts from "../../../lib/appconst";
import testSetService from "../../../services/testSet/testSetService";
import InsuranceCompanyStore from "../../../stores/insuranceCompanyStore";
import Stores from "../../../stores/storeIdentifier";
import { myTheme } from "../../../styles/theme";
import { conditionalAttribute, getAdjustInputsValues, getConditionalResultFromAttrJsonProps, mapGnInsurerNameToId, renderElement } from "../../../utils/policyCalculationUtils";
import { filterBySome, isJsonString } from "../../../utils/utils";
import { Controls } from "../../BaseComponents/controls";
import { getInputIconData } from "../../../utils/inputUtils";
import { defaultTestSet } from "../../../stores/testSetStore";

const customInputsStackStyles: IStackStyles = {
    root: {
        display: 'flex',
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'start',
        alignItems: 'center',
        width: '100%',
        marginTop: '10px',
    },
};

const customInputsStackTokens: IStackTokens = {
    childrenGap: '10',
    padding: '25px 0 10px',
};

const classNames = mergeStyleSets({
    hide: {
        display: 'none !important',
    },
    opacityHide: {
        visibility: 'hidden !important',
    },
    fontBold: {
        fontWeight: '800',
    },
    toolbar: {
        selectors: {
            '& .ms-Pivot': {
                display: 'none',
            }
        }
    },
    messageBar: {
        width: 'fit-content',
        whiteSpace: 'pre-line',
    },
    messageBarMargin: {
        marginTop: '15px'
    },
    customInputsWrapper: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        width: '100%',
        height: 'auto',
        maxWidth: '1200px',
        marginTop: '20px',
        padding: '20px',
        paddingTop: '0',
        boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.25)',
        borderRadius: '12px',
        minWidth: '502px',
        boxSizing: 'border-box',
    },
    loadSpinner: {
        display: 'inline-flex',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    recalculateButton: {
        // marginLeft: '15%',
    },
    inputsArray: {
        '& div.checkboxOptionsWrapper-64': {
            marginLeft: '10px !important',
            marginBottom: '10px !important'
        }
    },
    customScrollablePane: {
        width: '100%',
        minHeight: '380px',
        position: 'relative',
        marginTop: '20px',
        selectors: {
            '& .ms-DetailsList.is-horizontalConstrained': {
                overflow: 'unset',
            }
        }
    },
    customDialog: {
        maxWidth: 'unset !important',
        minWidth: '500px !important',
        width: '80% !important',
        minHeight: '420px',
        selectors: {
            '.ms-Dialog-content': {
                selectors: {
                    '.ms-Dialog-header': {
                        display: 'none',
                    }
                }
            }
        }
    },
    dialogDisclaimers: {
        display: 'block',
        fontSize: '12px',
        textAlign: 'left',
        marginLeft: '25px',
        whiteSpace: 'pre-line',
    }
});

const headerStyle: Partial<IDetailsColumnStyles> = {
    cellTitle: {
        textAlign: 'center',
        display: 'block',
        width: '100%',
        maxWidth: '270px',
    }
}

export interface IAdjustValuesCustomInputsBoxProps {
    templateInputsForCalculationAdjust: any;
    messageBoxData: any;
    asyncActionInProgress: boolean | undefined;
    selectedCalculation: any;
    calculations: any;
    showCustomInputsBox: boolean;
    customInputsForCalculationIndex: number;
    step3This: any;
    gnLanguage: any;
    insuranceCompanyStore?: InsuranceCompanyStore;
    inputsTypeValuePairs: any;
    inputsIdUserFieldsPairs: any;
    product: any;
    productAttributes: any;
    adjustInputsChangedManually: any;
    lastGetCalculationPayload: any;
    disabledInputsData: any;
    variantsTableDataShowDialog: boolean;
    currentVariantsTableIconInputId: string | undefined;
    setInputsUserFields: (id: string, userFields: any) => void;
    getSingleCalculation: (insurerName: string) => void;
    onInputChange: (id: string, value: any, userFields: any) => void;
    toggleVariantsTableDataShowDialog: (value: boolean) => void;
}

type IAdjustValuesCustomInputsBoxState = {
    newOptionsSetFor: any,
    disableSavePayloadAsTestButton: boolean | null,
};

@inject(Stores.InsuranceCompanyStore)
@observer
export class AdjustValuesCustomInputsBox extends React.Component<IAdjustValuesCustomInputsBoxProps, IAdjustValuesCustomInputsBoxState> {
    private lastRenderTimestamp: number = 0;
    private lastSetDefaultValueTimestamp: number = 0;
    private lastShowBoxTimestamp: number = 0;
    private conditionalDefaultValueAlreadySetFor: any[] = [];
    private inputsToSetAfterBoxRender: any[] = [];
    private variantsTableData: any = {};
    private variantsTableColumns: any = {};
    private variantsTableItems: any = {};
    private variantsTableTitle: any = {};
    private variantsTableDisclaimers: any = {};
    private reRenderCounter: number = 0;
    private setVariantsTableDataCounter: {[key: string]: number} = {};
    
    constructor(props: IAdjustValuesCustomInputsBoxProps) {
        super(props);
    
        this.state = {
            ...this.state,
            newOptionsSetFor: {
                selectedInsurer: "" as string,
                optionsSetForInputId: [] as string[],
            },
            disableSavePayloadAsTestButton: null,
        };
    }

    async componentDidMount() {
        const { insuranceCompanyStore } = this.props;
        if(insuranceCompanyStore && (!insuranceCompanyStore.dataSet || insuranceCompanyStore.dataSet.totalCount <= 0)) {
            await insuranceCompanyStore.getAll();
            this.forceUpdate();
        }
    }

    private setVariantsTableData(setForInputId: string) {
        const currentLanguage: string = this.props.gnLanguage.UniqueSeoCode;

        let tempTableItems: any[] = [];

        if(this.variantsTableData[setForInputId]) {
            const variantsTableData: any = this.variantsTableData[setForInputId];

            if(variantsTableData.Table && variantsTableData.Table.Locales) {
                variantsTableData.Table.Locales.forEach((locale: any) => {
                    if(locale.Language === this.props.gnLanguage.UniqueSeoCode) {
                        this.variantsTableTitle[setForInputId] = locale.LocaleValue;
                    }
                });
            }

            if(variantsTableData.Disclaimers) {
                this.variantsTableDisclaimers[setForInputId] = '';

                variantsTableData.Disclaimers.forEach((disclaimer: any) => {
                    if(disclaimer.Language === this.props.gnLanguage.UniqueSeoCode) {
                        disclaimer.LocaleValue.forEach((disclaimerValue: string) => {
                            this.variantsTableDisclaimers[setForInputId] = this.variantsTableDisclaimers[setForInputId].length === 0 ? disclaimerValue : this.variantsTableDisclaimers[setForInputId] + `\n\r${disclaimerValue}`;
                        });
                    }
                });
            }
    
            variantsTableData.Rows.forEach((row: any, rowIndex: number) => {
                row.Cells.forEach((cell: any) => {
                    if(cell.Language === currentLanguage) {
                        if(!this.variantsTableColumns[setForInputId]) {
                            this.variantsTableColumns[setForInputId] = [];
                        }

                        if(rowIndex === 0) {
                            cell.LocaleValue.forEach((value: string, valueIndex: number) => {
                                this.variantsTableColumns[setForInputId].push({
                                    key: valueIndex === 0 ? 'nameCol' : `col_${valueIndex}`,
                                    minWidth: valueIndex === 0 ? 220 : 270,
                                    maxWidth: valueIndex === 0 ? 220 : 270,
                                    name: valueIndex === 0 ? '' : L(value),
                                    fieldName: valueIndex === 0 ? 'nameCol' : `col_${valueIndex}`,
                                    styles: headerStyle,
                                    isResizable: true,
                                    onRender: (item: any) => {
                                        if(valueIndex === 0) {
                                            return <span title={item['nameCol']} style={{fontWeight: 'bold'}}>{item['nameCol']}</span>;
                                        } else {
                                            return <span title={item[`col_${valueIndex}`]} style={{textAlign: 'center', display: 'block', width: '100%', 
                                                maxWidth: '270px', whiteSpace: 'pre-line'}}
                                            >
                                                {item[`col_${valueIndex}`] === 'ptaszek' ? '✓' : item[`col_${valueIndex}`]}
                                            </span>;
                                        }
                                    }
                                });
                            });
                        } else {
                            cell.LocaleValue.forEach((value: string, localeValueIndex: number) => {
                                const newRowIndex: number = rowIndex - 1;

                                if(!tempTableItems[newRowIndex]) {
                                    tempTableItems[newRowIndex] = {};
                                }

                                if(localeValueIndex === 0) {
                                    tempTableItems[newRowIndex][`nameCol`] = `${value}`;
                                } else {
                                    tempTableItems[newRowIndex][`col_${localeValueIndex}`] = `${value}`;
                                }
                            });
                        }
                    }
                });
            });

            this.variantsTableItems[setForInputId] = tempTableItems;
            this.setVariantsTableDataCounter[setForInputId] = !this.setVariantsTableDataCounter[setForInputId] ? 1 : this.setVariantsTableDataCounter[setForInputId] + 1;

            this.forceUpdate();
        }
    }

    private setDefaultInputsData(inputsToSet: any[], omitCheckIfInputsChagnedManually: boolean = false) {
        this.inputsToSetAfterBoxRender = [];

        if(inputsToSet.length > 0) {
            inputsToSet.forEach((element) => {
                if(omitCheckIfInputsChagnedManually === false && this.props.adjustInputsChangedManually[element.id] && !element.forceOmitCheckIfInputsChagnedManually) {
                    return;
                }
                this.props.onInputChange(element.id, element.value, element.userFields);
            });
            inputsToSet = [];
        }
    }

    private checkIfAttributeHidden(UserFields: any): boolean {
        let returnValue: boolean = false;
        UserFields.some((UserField: any) => {
            if(UserField.Key === 'Hide' && (UserField.Value === 'true' || UserField.Value === true)) {
                returnValue = true;
                return true;
            }
            return false;
        });
        return returnValue;
    }

    private getAttributeOrder(UserFields: any): number {
        const filteredUserField: any = filterBySome(UserFields, 'Key', 'step_insurer');

        if(!!filteredUserField) {
            return parseInt(filteredUserField.Value);
        } else {
            return 0;
        }
    }

    private async saveRecalculatePayloadAsTest() {
        if(this.props && this.props.lastGetCalculationPayload && this.props.lastGetCalculationPayload.payload && this.props.lastGetCalculationPayload.insurerName) {
            const calculationId: number = this.props.calculations && this.props.calculations.data && this.props.calculations.data.result &&
                                        this.props.calculations.data.result.calculationId ? this.props.calculations.data.result.calculationId : 0;

            await testSetService.create({
                ...defaultTestSet,
                name: `${calculationId} (${L('Saved from calculation')})`,
                payload: JSON.stringify(this.props.lastGetCalculationPayload.payload),
                insurerName: this.props.lastGetCalculationPayload.insurerName,
            }).then(async (response: any) => {
                if(response && response.id && response.id > 0) {
                    this.setState({ disableSavePayloadAsTestButton: true });
                    await testSetService.sendTestCalculation(response.id);
                } else if(response && response.error) {
                    console.error(response.error);
                }
            }).catch((error: any) => {
                console.error(error);
            });
        }
    }

    private checkIfInputOrOptionShouldBeDisabled(property: any): any {
        let cloneProperty: any = {...property};
        let clonePropertyOptions: any = {
            dropdown: [] as IDropdownOption[],
            choicegroup: [] as IChoiceGroupOption[],
        };

        cloneProperty.disabled = this.props.disabledInputsData.inputsIds.includes(property.id) ? true : cloneProperty.disabled;

        cloneProperty.options.choicegroup.forEach((option: IChoiceGroupOption) => {
            clonePropertyOptions.choicegroup.push({...option, disabled: this.props.disabledInputsData.optionsIds.includes(option.key) ? true : option.disabled});
        });
        cloneProperty.options.dropdown.forEach((option: IDropdownOption) => {
            clonePropertyOptions.dropdown.push({...option, disabled: this.props.disabledInputsData.optionsIds.includes(option.key) ? true : option.disabled});
        });
        cloneProperty.options.choicegroup = clonePropertyOptions.choicegroup;
        cloneProperty.options.dropdown = clonePropertyOptions.dropdown;

        return cloneProperty;
    }

    render() {
        const { templateInputsForCalculationAdjust, messageBoxData, asyncActionInProgress, showCustomInputsBox, product,
                customInputsForCalculationIndex, insuranceCompanyStore, inputsTypeValuePairs, inputsIdUserFieldsPairs, currentVariantsTableIconInputId } = this.props;
        const selectedCalculationGnInsurerId = this.props.calculations && this.props.calculations.data.result.policyCalculations[customInputsForCalculationIndex];

        for(let inputId in this.variantsTableData) {
            if(this.variantsTableData.hasOwnProperty(inputId) && this.variantsTableData[inputId] && 
                (!this.variantsTableItems[inputId] || this.variantsTableItems[inputId].length === 0) && 
                (!this.setVariantsTableDataCounter[inputId] || this.setVariantsTableDataCounter[inputId] < 5)
            ) {
                this.setVariantsTableData(inputId);
            }
        }

        const waitForInputsToRenderAfterBoxShow: number = 1500;
        if(showCustomInputsBox === true && this.lastShowBoxTimestamp === 0) {
            this.lastShowBoxTimestamp = + new Date();

            setTimeout(() => {
                this.forceUpdate();
            }, waitForInputsToRenderAfterBoxShow + 50);
        } else if(showCustomInputsBox === false) {
            this.lastShowBoxTimestamp = 0;
        }

        if(selectedCalculationGnInsurerId && selectedCalculationGnInsurerId.gnInsurerId !== this.state.newOptionsSetFor.selectedInsurer) {
            this.lastShowBoxTimestamp = 0;
            this.setState({ newOptionsSetFor: { selectedInsurer: selectedCalculationGnInsurerId.gnInsurerId, optionsSetForInputId: [] }});
        }

        if(Object.keys(templateInputsForCalculationAdjust).length > 0 && (this.props.selectedCalculation || selectedCalculationGnInsurerId) && (insuranceCompanyStore?.dataSet && insuranceCompanyStore?.dataSet.totalCount > 0)) {
            let inputsArray: JSX.Element[] = [];
            let inputsToSet: any[] = [];
            let shouldMassUpdateInputs: boolean = false;
            let inputsIndexAndOrder: any[] = [];
            let now = + new Date();

            for(let key in templateInputsForCalculationAdjust) {
                if(templateInputsForCalculationAdjust.hasOwnProperty(key) && !this.checkIfAttributeHidden(templateInputsForCalculationAdjust[key].userFields)) {
                    let userFields = [...templateInputsForCalculationAdjust[key].userFields];
                    let attributeOrder: number = this.getAttributeOrder(userFields);
                    let conditionalOptionsResult: any = {};
                    
                    if(!this.props.inputsIdUserFieldsPairs[key]) {
                        this.props.setInputsUserFields(key, userFields);
                    }

                    const allowForInsurersUserField: any = userFields.filter((userField: any) => userField.Key === "allowTuningForInsurer");
                    const allowForInsurersValuesUserField: any = userFields.filter((userField: any) => userField.Key === "allowTuningForInsurerValues");
                    let allowTuningPreselectedKey: any = userFields.filter((userField: any) => userField.Key === "allowTuningPreselected");
                    if(allowTuningPreselectedKey && allowTuningPreselectedKey.length > 0) {
                        allowTuningPreselectedKey = allowTuningPreselectedKey[0].Value;
                    }

                    let allowForInsurers: string[] = allowForInsurersUserField.length > 0 ? allowForInsurersUserField[0].Value.split(";;") : [];
                    let allowForInsurerValue: string = allowForInsurersValuesUserField.length > 0 ? allowForInsurersValuesUserField[0].Value : "";
                    
                    let convertedAllowForInsurers: string[] = [];
                    allowForInsurers.forEach((insurerName: string, index: number) => {
                        convertedAllowForInsurers[index] = mapGnInsurerNameToId(insurerName, insuranceCompanyStore.dataSet.items);
                    });
                    allowForInsurers = convertedAllowForInsurers;

                    if(templateInputsForCalculationAdjust[key].attr.DefaultValue && !this.props.inputsTypeValuePairs[templateInputsForCalculationAdjust[key].attr.Id]) {
                        inputsToSet.push({id: templateInputsForCalculationAdjust[key].attr.Id, value: templateInputsForCalculationAdjust[key].attr.DefaultValue, userFields: userFields});
                    }

                    if(selectedCalculationGnInsurerId && allowForInsurers.includes(selectedCalculationGnInsurerId.gnInsurerId)) {
                        now = + new Date();
                        const itemsAttrJsonPropsUserField: any = filterBySome(userFields, "Key", "items_attr_json_props");

                        if(!this.variantsTableData[key] && this.variantsTableData[key] !== null) {
                            let variantsTableUserField: any;
                            
                            variantsTableUserField = filterBySome(userFields, "Key", "olo_clever_variants_table");
                            if(!variantsTableUserField) {
                                variantsTableUserField = filterBySome(userFields, "Key", "variants_table");
                            }

                            if(variantsTableUserField) {
                                this.variantsTableData[key] = isJsonString(variantsTableUserField.Value) ? JSON.parse(variantsTableUserField.Value) : null;
                            }
                        }

                        if(itemsAttrJsonPropsUserField && itemsAttrJsonPropsUserField.Value) {
                            const parsedItemsAttrJsonPropsUserField: any = JSON.parse(itemsAttrJsonPropsUserField.Value);
                            for(let optionName in parsedItemsAttrJsonPropsUserField) {
                                conditionalOptionsResult[optionName] = getConditionalResultFromAttrJsonProps(parsedItemsAttrJsonPropsUserField[optionName], this.props.inputsTypeValuePairs, 
                                                            this.props.product.ProductAttributeMappings, this.props.productAttributes, this.props.gnLanguage, true);
                            }
                        }

                        if(allowForInsurerValue && allowForInsurerValue.length > 0 && !this.state.newOptionsSetFor.optionsSetForInputId.includes(templateInputsForCalculationAdjust[key].property.id)) {
                            const defaultAdjustInputsValuesPairs = getAdjustInputsValues(allowForInsurerValue, this.props.gnLanguage, this.props.insuranceCompanyStore);
                            let newOptions = {...templateInputsForCalculationAdjust[key].property.options};

                            switch(templateInputsForCalculationAdjust[key].property.type) {
                                case Controls.Picker:
                                case Controls.CheckBox:
                                case Controls.CheckBoxOptions:
                                    newOptions['dropdown'] = [];
                                    for(let optionKey in defaultAdjustInputsValuesPairs[selectedCalculationGnInsurerId.gnInsurerId]) {
                                        if(defaultAdjustInputsValuesPairs[selectedCalculationGnInsurerId.gnInsurerId].hasOwnProperty(optionKey)) {
                                            if(typeof allowTuningPreselectedKey === 'string' && allowTuningPreselectedKey === defaultAdjustInputsValuesPairs[selectedCalculationGnInsurerId.gnInsurerId][optionKey].key
                                                && !(typeof inputsTypeValuePairs[templateInputsForCalculationAdjust[key].property.id] !== 'undefined' &&
                                                    inputsTypeValuePairs[templateInputsForCalculationAdjust[key].property.id] === defaultAdjustInputsValuesPairs[selectedCalculationGnInsurerId.gnInsurerId][optionKey].key)
                                            ) {
                                                this.props.onInputChange(templateInputsForCalculationAdjust[key].property.id, allowTuningPreselectedKey, userFields);
                                            }

                                            const optionName: string = defaultAdjustInputsValuesPairs[selectedCalculationGnInsurerId.gnInsurerId][optionKey].name;
                                            newOptions.dropdown.push({
                                                key: defaultAdjustInputsValuesPairs[selectedCalculationGnInsurerId.gnInsurerId][optionKey].key, 
                                                text: defaultAdjustInputsValuesPairs[selectedCalculationGnInsurerId.gnInsurerId][optionKey].text, 
                                                isSelected: typeof inputsTypeValuePairs[templateInputsForCalculationAdjust[key].property.id] !== 'undefined' &&
                                                            inputsTypeValuePairs[templateInputsForCalculationAdjust[key].property.id] === defaultAdjustInputsValuesPairs[selectedCalculationGnInsurerId.gnInsurerId][optionKey].key 
                                                            ? true : false,
                                                disabled: typeof conditionalOptionsResult[optionName] !== 'undefined' ? conditionalOptionsResult[optionName].disabled : false,
                                                hidden: typeof conditionalOptionsResult[optionName] !== 'undefined' ? !conditionalOptionsResult[optionName].show : false
                                            });
                                        }
                                    }
                                    break;
                                // case Controls.CheckBox:
                                // case Controls.CheckBoxOptions:
                                //     newOptions['checkbox'] = [];
                                //     for(let optionKey in defaultAdjustInputsValuesPairs[selectedCalculationGnInsurerId.gnInsurerId]) {
                                //         if(defaultAdjustInputsValuesPairs[selectedCalculationGnInsurerId.gnInsurerId].hasOwnProperty(optionKey)) {
                                //             newOptions.checkbox.push({
                                //                 key: defaultAdjustInputsValuesPairs[selectedCalculationGnInsurerId.gnInsurerId][optionKey].key, 
                                //                 text: defaultAdjustInputsValuesPairs[selectedCalculationGnInsurerId.gnInsurerId][optionKey].text, 
                                //                 // isSelected: inputsTypeValuePairs[key]
                                //             });
                                //         }
                                //     }
                                //     break;
                                case Controls.ChoiceGroup:
                                    newOptions['choicegroup'] = [];
                                    for(let optionKey in defaultAdjustInputsValuesPairs[selectedCalculationGnInsurerId.gnInsurerId]) {
                                        if(defaultAdjustInputsValuesPairs[selectedCalculationGnInsurerId.gnInsurerId].hasOwnProperty(optionKey)) {
                                            const optionName: string = defaultAdjustInputsValuesPairs[selectedCalculationGnInsurerId.gnInsurerId][optionKey].name;
                                            newOptions.choicegroup.push({
                                                key: defaultAdjustInputsValuesPairs[selectedCalculationGnInsurerId.gnInsurerId][optionKey].key, 
                                                text: defaultAdjustInputsValuesPairs[selectedCalculationGnInsurerId.gnInsurerId][optionKey].text, 
                                                // checked: inputsTypeValuePairs[key],
                                                disabled: (this.props.disabledInputsData.optionsIds.includes(defaultAdjustInputsValuesPairs[selectedCalculationGnInsurerId.gnInsurerId][optionKey].key) ||
                                                            (typeof conditionalOptionsResult[optionName] !== 'undefined' && conditionalOptionsResult[optionName].disabled)) ? 
                                                                true : false,
                                                hidden: typeof conditionalOptionsResult[optionName] !== 'undefined' ? !conditionalOptionsResult[optionName].show : false,
                                            });
                                        }
                                    }
                                    break;
                            }
                            
                            templateInputsForCalculationAdjust[key].property.options = newOptions;

                            const conditionalAttributeResult: any = conditionalAttribute(templateInputsForCalculationAdjust[key].attr, inputsTypeValuePairs, inputsIdUserFieldsPairs, product.ProductAttributeMappings, this.props.productAttributes, this.props.gnLanguage);

                            let valuesAreTheSame: boolean = false;
                            if(typeof inputsTypeValuePairs[templateInputsForCalculationAdjust[key].property.id] !== 'undefined' && typeof this.conditionalDefaultValueAlreadySetFor[templateInputsForCalculationAdjust[key].property.id] !== 'undefined') {
                                if(typeof inputsTypeValuePairs[templateInputsForCalculationAdjust[key].property.id] !== 'object') {
                                    valuesAreTheSame = inputsTypeValuePairs[templateInputsForCalculationAdjust[key].property.id] === this.conditionalDefaultValueAlreadySetFor[templateInputsForCalculationAdjust[key].property.id].settedValue;
                                } else {
                                    valuesAreTheSame = JSON.stringify(inputsTypeValuePairs[templateInputsForCalculationAdjust[key].property.id]) === JSON.stringify(this.conditionalDefaultValueAlreadySetFor[templateInputsForCalculationAdjust[key].property.id].settedValue);
                                }
                            }

                            const nowMinusLastSetDefaultTimestamp: number = now - this.lastSetDefaultValueTimestamp;
                            if(typeof conditionalAttributeResult.newValue !== 'undefined' && typeof conditionalAttributeResult.newValue === 'string' && 
                                (!this.conditionalDefaultValueAlreadySetFor[templateInputsForCalculationAdjust[key].property.id] || 
                                    this.conditionalDefaultValueAlreadySetFor[templateInputsForCalculationAdjust[key].property.id].value !== conditionalAttributeResult.newValue || 
                                    ((nowMinusLastSetDefaultTimestamp >= 0 && nowMinusLastSetDefaultTimestamp < 50) && (conditionalAttributeResult.newValue.length > 0 && !valuesAreTheSame))
                                )
                            ) {
                                let valueToSet: string = conditionalAttributeResult.newValue;
                                let inputsToSetUpdated: boolean = false;

                                if(templateInputsForCalculationAdjust[key].property.type === Controls.ChoiceGroup || 
                                    templateInputsForCalculationAdjust[key].property.type === Controls.CheckBoxOptions
                                ) {
                                    if(valueToSet.length === 0) {
                                        let optionsToSet: any = {};
                                        templateInputsForCalculationAdjust[key].attr.ProductAttributeValues.forEach((attrValue: any) => {
                                            optionsToSet[attrValue.Id] = false;
                                        });

                                        inputsToSetUpdated = true;
                                        inputsToSet.push({id: templateInputsForCalculationAdjust[key].property.id, value: optionsToSet, userFields: userFields,
                                                            forceOmitCheckIfInputsChagnedManually: conditionalAttributeResult.forceOmitCheckIfInputsChagnedManually});
                                    } else {
                                        let splittedValue: string[] = valueToSet.split(', ');
                                        let optionsToSet: any = {};
    
                                        let optionsAlreadySetToTrue: string[] = [];
                                        splittedValue.forEach((value: string) => {
                                            templateInputsForCalculationAdjust[key].attr.ProductAttributeValues.forEach((attrValue: any) => {
                                                if(value === attrValue.Name) {
                                                    optionsToSet[attrValue.Id] = true;
                                                    optionsAlreadySetToTrue.push(attrValue.Id);
                                                } else if(!optionsAlreadySetToTrue.includes(attrValue.Id)) {
                                                    optionsToSet[attrValue.Id] = false;
                                                }
                                            });
                                        });
    
                                        if(Object.keys(optionsToSet).length > 0) {
                                            valueToSet = optionsToSet;
                                            inputsToSetUpdated = true;
                                            inputsToSet.push({id: templateInputsForCalculationAdjust[key].property.id, value: valueToSet, userFields: userFields,
                                                                forceOmitCheckIfInputsChagnedManually: conditionalAttributeResult.forceOmitCheckIfInputsChagnedManually});
                                        }
                                    }
                                } else if(templateInputsForCalculationAdjust[key].property.type === Controls.Picker) {
                                    const filteredAttrValue: any = filterBySome(templateInputsForCalculationAdjust[key].attr.ProductAttributeValues, 'Name', valueToSet);

                                    if(filteredAttrValue && filteredAttrValue.Id && inputsTypeValuePairs[templateInputsForCalculationAdjust[key].property.id] !== filteredAttrValue.Id) { 
                                        valueToSet = filteredAttrValue.Id;
                                        inputsToSetUpdated = true;
                                        inputsToSet.push({id: templateInputsForCalculationAdjust[key].property.id, value: valueToSet, userFields: userFields,
                                                            forceOmitCheckIfInputsChagnedManually: conditionalAttributeResult.forceOmitCheckIfInputsChagnedManually});
                                    }
                                } else if(inputsTypeValuePairs[templateInputsForCalculationAdjust[key].property.id] !== valueToSet) { 
                                    inputsToSetUpdated = true;
                                    inputsToSet.push({id: templateInputsForCalculationAdjust[key].property.id, value: valueToSet, userFields: userFields,
                                                        forceOmitCheckIfInputsChagnedManually: conditionalAttributeResult.forceOmitCheckIfInputsChagnedManually});
                                }
                                
                                if(inputsToSetUpdated === true) {
                                    this.lastSetDefaultValueTimestamp = + new Date();

                                    this.conditionalDefaultValueAlreadySetFor[templateInputsForCalculationAdjust[key].property.id] = {
                                        value: conditionalAttributeResult.newValue,
                                        settedValue: valueToSet
                                    };

                                    shouldMassUpdateInputs = true;
                                }
                            }

                            if(conditionalAttributeResult.show === true) {
                                if(conditionalAttributeResult.disabled === true) {
                                    templateInputsForCalculationAdjust[key].property.disabled = true;
                                }

                                inputsArray.push(
                                    renderElement(this.checkIfInputOrOptionShouldBeDisabled(templateInputsForCalculationAdjust[key].property), conditionalAttributeResult.show,
                                                {}, this.props.step3This)
                                );
                            } else {
                                delete inputsTypeValuePairs[templateInputsForCalculationAdjust[key].attr.Id];
                                if(nowMinusLastSetDefaultTimestamp >= 100 && this.reRenderCounter === 0) {
                                    delete this.conditionalDefaultValueAlreadySetFor[templateInputsForCalculationAdjust[key].property.id];
                                    this.reRenderCounter = this.reRenderCounter + 1;
                                }
                            }

                            if(nowMinusLastSetDefaultTimestamp > 2000) {
                                this.reRenderCounter = 0;
                            }

                            this.setState({ newOptionsSetFor: { selectedInsurer: this.state.newOptionsSetFor.selectedInsurer, optionsSetForInputId: [...this.state.newOptionsSetFor.optionsSetForInputId, templateInputsForCalculationAdjust[key].property.id] }});
                        } else {
                            const conditionalAttributeResult: any = conditionalAttribute(templateInputsForCalculationAdjust[key].attr, inputsTypeValuePairs, inputsIdUserFieldsPairs, product.ProductAttributeMappings, this.props.productAttributes, this.props.gnLanguage);

                            switch(templateInputsForCalculationAdjust[key].property.type) {
                                // case Controls.Picker:
                                //     let dropdownOptions = templateInputsForCalculationAdjust[key].property.options.dropdown;
                                //     for(let optionKey in dropdownOptions) {
                                //         console(...)(dropdownOptions, dropdownOptions[optionKey]);
                                //     }
                                //     break;
                                case Controls.Picker:
                                case Controls.CheckBox:
                                case Controls.CheckBoxOptions:
                                case Controls.ChoiceGroup:
                                    let originCheckboxOptions: boolean = true;
                                    let checkboxOptions = templateInputsForCalculationAdjust[key].property.options.checkbox;
                                    if(!checkboxOptions || checkboxOptions.length === 0) {
                                        checkboxOptions = templateInputsForCalculationAdjust[key].property.options.dropdown;
                                        originCheckboxOptions = false;
                                    }

                                    for(let optionKey in checkboxOptions) {
                                        const optionName: string = checkboxOptions[optionKey].name;

                                        if(inputsTypeValuePairs[templateInputsForCalculationAdjust[key].property.id] && typeof inputsTypeValuePairs[templateInputsForCalculationAdjust[key].property.id][checkboxOptions[optionKey].key] !== 'undefined') {
                                            checkboxOptions[optionKey].isSelected = inputsTypeValuePairs[templateInputsForCalculationAdjust[key].property.id][checkboxOptions[optionKey].key];
                                        }
                                        checkboxOptions[optionKey].disabled = (this.props.disabledInputsData.optionsIds.includes(checkboxOptions[optionKey].key) ||
                                                                                (typeof conditionalOptionsResult[optionName] !== 'undefined' && conditionalOptionsResult[optionName].disabled)) ? 
                                                                                    true : false;
                                        checkboxOptions[optionKey].hidden = typeof conditionalOptionsResult[optionName] !== 'undefined' ? !conditionalOptionsResult[optionName].show : false;
                                    }

                                    if(originCheckboxOptions) {
                                        templateInputsForCalculationAdjust[key].property.options.checkbox = checkboxOptions;
                                    } else {
                                        templateInputsForCalculationAdjust[key].property.options.dropdown = checkboxOptions;
                                    }
                                    break;
                                // case Controls.ChoiceGroup:
                                    // let choicegroupOptions = templateInputsForCalculationAdjust[key].property.options.choicegroup;
                                    
                                    // for(let optionKey in choicegroupOptions) {
                                    //     if(inputsTypeValuePairs[templateInputsForCalculationAdjust[key].property.id] && typeof inputsTypeValuePairs[templateInputsForCalculationAdjust[key].property.id][choicegroupOptions[optionKey].key] !== 'undefined') {
                                    //         choicegroupOptions[optionKey].isSelected = inputsTypeValuePairs[templateInputsForCalculationAdjust[key].property.id][choicegroupOptions[optionKey].key];
                                    //     }
                                    // }

                                    // templateInputsForCalculationAdjust[key].property.options.choicegroup = choicegroupOptions;
                                    // break;
                            }

                            let valuesAreTheSame: boolean = false;
                            if(typeof inputsTypeValuePairs[templateInputsForCalculationAdjust[key].property.id] !== 'undefined' && typeof this.conditionalDefaultValueAlreadySetFor[templateInputsForCalculationAdjust[key].property.id] !== 'undefined') {
                                if(typeof inputsTypeValuePairs[templateInputsForCalculationAdjust[key].property.id] !== 'object') {
                                    valuesAreTheSame = inputsTypeValuePairs[templateInputsForCalculationAdjust[key].property.id] === this.conditionalDefaultValueAlreadySetFor[templateInputsForCalculationAdjust[key].property.id].settedValue;
                                } else {
                                    valuesAreTheSame = JSON.stringify(inputsTypeValuePairs[templateInputsForCalculationAdjust[key].property.id]) === JSON.stringify(this.conditionalDefaultValueAlreadySetFor[templateInputsForCalculationAdjust[key].property.id].settedValue);
                                }
                            }

                            const nowMinusLastSetDefaultTimestamp: number = now - this.lastSetDefaultValueTimestamp;
                            if(typeof conditionalAttributeResult.newValue !== 'undefined' && typeof conditionalAttributeResult.newValue === 'string' && 
                                (!this.conditionalDefaultValueAlreadySetFor[templateInputsForCalculationAdjust[key].property.id] || 
                                    this.conditionalDefaultValueAlreadySetFor[templateInputsForCalculationAdjust[key].property.id].value !== conditionalAttributeResult.newValue || 
                                    ((nowMinusLastSetDefaultTimestamp >= 0 && nowMinusLastSetDefaultTimestamp < 50) && (conditionalAttributeResult.newValue.length > 0 && !valuesAreTheSame))
                                )
                            ) {
                                let valueToSet: string = conditionalAttributeResult.newValue;
                                let inputsToSetUpdated: boolean = false;

                                if(templateInputsForCalculationAdjust[key].property.type === Controls.ChoiceGroup || 
                                    templateInputsForCalculationAdjust[key].property.type === Controls.CheckBoxOptions
                                ) {
                                    if(valueToSet.length === 0) {
                                        let optionsToSet: any = {};
                                        templateInputsForCalculationAdjust[key].attr.ProductAttributeValues.forEach((attrValue: any) => {
                                            optionsToSet[attrValue.Id] = false;
                                        });

                                        inputsToSetUpdated = true;
                                        inputsToSet.push({id: templateInputsForCalculationAdjust[key].property.id, value: optionsToSet, userFields: userFields,
                                                            forceOmitCheckIfInputsChagnedManually: conditionalAttributeResult.forceOmitCheckIfInputsChagnedManually});
                                    } else {
                                        let splittedValue: string[] = valueToSet.split(', ');
                                        let optionsToSet: any = {};
    
                                        let optionsAlreadySetToTrue: string[] = [];
                                        splittedValue.forEach((value: string) => {
                                            templateInputsForCalculationAdjust[key].attr.ProductAttributeValues.forEach((attrValue: any) => {
                                                if(value === attrValue.Name) {
                                                    optionsToSet[attrValue.Id] = true;
                                                    optionsAlreadySetToTrue.push(attrValue.Id);
                                                } else if(!optionsAlreadySetToTrue.includes(attrValue.Id)) {
                                                    optionsToSet[attrValue.Id] = false;
                                                }
                                            });
                                        });
    
                                        if(Object.keys(optionsToSet).length > 0) {
                                            valueToSet = optionsToSet;
                                            inputsToSetUpdated = true;
                                            inputsToSet.push({id: templateInputsForCalculationAdjust[key].property.id, value: valueToSet, userFields: userFields,
                                                                forceOmitCheckIfInputsChagnedManually: conditionalAttributeResult.forceOmitCheckIfInputsChagnedManually});
                                        }
                                    }
                                } else if(templateInputsForCalculationAdjust[key].property.type === Controls.Picker) {
                                    const filteredAttrValue: any = filterBySome(templateInputsForCalculationAdjust[key].attr.ProductAttributeValues, 'Name', valueToSet);

                                    if(filteredAttrValue && filteredAttrValue.Id && inputsTypeValuePairs[templateInputsForCalculationAdjust[key].property.id] !== filteredAttrValue.Id) { 
                                        valueToSet = filteredAttrValue.Id;
                                        inputsToSetUpdated = true;
                                        inputsToSet.push({id: templateInputsForCalculationAdjust[key].property.id, value: valueToSet, userFields: userFields,
                                                            forceOmitCheckIfInputsChagnedManually: conditionalAttributeResult.forceOmitCheckIfInputsChagnedManually});
                                    }
                                } else if(inputsTypeValuePairs[templateInputsForCalculationAdjust[key].property.id] !== valueToSet) { 
                                    inputsToSetUpdated = true;
                                    inputsToSet.push({id: templateInputsForCalculationAdjust[key].property.id, value: valueToSet, userFields: userFields,
                                                        forceOmitCheckIfInputsChagnedManually: conditionalAttributeResult.forceOmitCheckIfInputsChagnedManually});
                                }
                                
                                if(inputsToSetUpdated === true) {
                                    this.lastSetDefaultValueTimestamp = + new Date();

                                    this.conditionalDefaultValueAlreadySetFor[templateInputsForCalculationAdjust[key].property.id] = {
                                        value: conditionalAttributeResult.newValue,
                                        settedValue: valueToSet
                                    };

                                    shouldMassUpdateInputs = true;
                                }
                            }

                            if(conditionalAttributeResult.show) {
                                if(conditionalAttributeResult.disabled === true) {
                                    templateInputsForCalculationAdjust[key].property.disabled = true;
                                }

                                let iconData: any = {};

                                if(this.variantsTableData[key]) {
                                    iconData = {
                                        iconName: "Info",
                                        iconOnClick: "toggleVariantsTableDialog",
                                        iconDataToPass: key,
                                    };
                                } else {
                                    iconData = getInputIconData(undefined, userFields);
                                    iconData['iconDataToPass'] = key;
                                }
                                
                                inputsIndexAndOrder.push({index: inputsArray.length, order: attributeOrder});
                                inputsArray.push(
                                    renderElement(this.checkIfInputOrOptionShouldBeDisabled(templateInputsForCalculationAdjust[key].property), conditionalAttributeResult.show,
                                                    iconData, this.props.step3This)
                                );
                            } else {
                                delete inputsTypeValuePairs[templateInputsForCalculationAdjust[key].attr.Id];
                                if(nowMinusLastSetDefaultTimestamp >= 100 && this.reRenderCounter === 0) {
                                    delete this.conditionalDefaultValueAlreadySetFor[templateInputsForCalculationAdjust[key].property.id];
                                    this.reRenderCounter = this.reRenderCounter + 1;
                                }
                            }

                            if(nowMinusLastSetDefaultTimestamp > 2000) {
                                this.reRenderCounter = 0;
                            }
                        }
                    }
                }
            }

            inputsIndexAndOrder.sort((a: any, b: any) => a.order > b.order ? 1 : -1);
            let orderedInputsArray: any[] = [];
            inputsIndexAndOrder.forEach((element: any) => {
                orderedInputsArray.push(<div className={classNames.inputsArray}>{inputsArray[element.index]}</div>);
            });

            if(shouldMassUpdateInputs === true) {
                if(this.lastShowBoxTimestamp < 0) {
                    this.setDefaultInputsData(inputsToSet);
                } else {
                    this.inputsToSetAfterBoxRender = [...inputsToSet];
                }
            }

            if(this.lastShowBoxTimestamp >= 0 && now - this.lastShowBoxTimestamp > waitForInputsToRenderAfterBoxShow && this.inputsToSetAfterBoxRender.length > 0) {
                this.lastShowBoxTimestamp = -1;
                this.setDefaultInputsData(this.inputsToSetAfterBoxRender, true);
            }

            this.lastRenderTimestamp = + new Date();

            return <FocusZone direction={FocusZoneDirection.horizontal} handleTabKey={FocusZoneTabbableElements.all} isCircularNavigation={true}>
                <div className={`${classNames.customInputsWrapper} ${showCustomInputsBox ? '' : classNames.hide}`}>
                    {(currentVariantsTableIconInputId && this.variantsTableData[currentVariantsTableIconInputId]) &&
                        <Dialog
                            hidden={!this.props.variantsTableDataShowDialog}
                            onDismiss={() => { this.props.toggleVariantsTableDataShowDialog(false); }}
                            dialogContentProps={{
                                type: DialogType.largeHeader,
                                title: this.variantsTableTitle[currentVariantsTableIconInputId],
                            }}
                            modalProps={{
                                isBlocking: true,
                                containerClassName: classNames.customDialog,
                            }}
                        >
                            <DialogContent>
                                <ScrollablePane scrollbarVisibility={ScrollbarVisibility.auto} theme={myTheme} className={classNames.customScrollablePane}>
                                    <MarqueeSelection isEnabled={false} selection={ new Selection({selectionMode: SelectionMode.none}) }>
                                        <ShimmeredDetailsList
                                            constrainMode={ConstrainMode.unconstrained}
                                            columns={this.variantsTableColumns[currentVariantsTableIconInputId] ? this.variantsTableColumns[currentVariantsTableIconInputId] : []}
                                            items={this.variantsTableItems[currentVariantsTableIconInputId] ? this.variantsTableItems[currentVariantsTableIconInputId] : []}
                                            selectionMode={SelectionMode.none}
                                            enableShimmer={false}
                                            compact={false}
                                        />
                                    </MarqueeSelection>
                                </ScrollablePane>
                            </DialogContent>

                            <DialogFooter>
                                {this.variantsTableDisclaimers[currentVariantsTableIconInputId] &&
                                    <p className={classNames.dialogDisclaimers}>{this.variantsTableDisclaimers[currentVariantsTableIconInputId]}</p>
                                }

                                <DefaultButton onClick={() => { this.props.toggleVariantsTableDataShowDialog(false); }} text={L("Close")} />
                            </DialogFooter>
                        </Dialog>
                    }

                    {orderedInputsArray}

                    <Stack horizontal styles={customInputsStackStyles} tokens={customInputsStackTokens}>
                        <PrimaryButton className={classNames.recalculateButton} theme={myTheme} text={L('Recalculate for selected offer')} type={'button'} 
                            onClick={() => {
                                this.props.getSingleCalculation(selectedCalculationGnInsurerId.insurerName);
                                this.setState({ disableSavePayloadAsTestButton: false });
                            }} disabled={asyncActionInProgress || inputsArray.length === 0} />

                        {AppConsts.allowedContent === 'ALL' && <PrimaryButton className={classNames.recalculateButton} theme={myTheme} text={L('Save payload for this recalculation as test')} iconProps={{ iconName: this.state.disableSavePayloadAsTestButton === true ? 'CheckMark' : 'Save' }}
                            type={'button'} onClick={() => this.saveRecalculatePayloadAsTest()} disabled={this.props.asyncActionInProgress || (this.state.disableSavePayloadAsTestButton || this.state.disableSavePayloadAsTestButton === null)} />
                        }

                        { <Spinner label={L('Please wait...')} className={`${classNames.loadSpinner} ${asyncActionInProgress ? '' : classNames.opacityHide}`} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" /> }
                    </Stack>

                    { (!messageBoxData.hide && messageBoxData.text.length > 0) && 
                        <MessageBar messageBarType={messageBoxData.type} isMultiline={true} className={`${classNames.messageBar} ${classNames.messageBarMargin}`}>
                            {L(messageBoxData.text)}
                        </MessageBar> }
                </div>
            </FocusZone>;
        } else {
            return <></>;
        }
    }
}