import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { L } from '../../../lib/abpUtility';
import { InsurancePolicyDto } from '../../../services/insurancePolicy/insurancePolicyDto';
import {additionalTheme, myTheme} from '../../../styles/theme';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { InsurancePolicyPanel } from './insurancePolicyPanel';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { RouterPath } from "../../../components/Router/router.config";
import { DefaultButton, Dialog, DialogFooter, DialogType, Icon, Link, PrimaryButton } from '@fluentui/react';
import { ICrudPermissons } from "../../BaseComponents/commandBarBase";
import insurancePolicyService from "../../../services/insurancePolicy/insurancePolicyService";
import { catchErrorMessage, dateFormat } from "../../../utils/utils";
import policyFinalizationService from "../../../services/policyFinalization/policyFinalizationService";
import { InsurancePolicyStatus } from "../../../services/insurancePolicy/insurancePolicyStatusEnums";
import { LabeledTextField } from "../../../components/LabeledTextField";

export class InsurancePolicyTable extends FluentTableBase<InsurancePolicyDto> {
  private shouldReloadItems: boolean = false;
  private showAcceptanceCodePopUpDialog: boolean = false;
  private showPopUpDialog: boolean = false;
  private popUpDialogTitle: string = "";
  private popUpDialogText: string = "";
  private uwAcceptanceCode: string = "";
  private saveMakePolicyProps: any = {
    policy: undefined,
    that: undefined,
  };

  getItemDisplayNameOf(item: InsurancePolicyDto): string {
    return item.offerNumber !== null ? item.offerNumber.toString() : "";
  }

  getColumns(): ITableColumn[] {
    return InsurancePolicyTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        minWidth: 25,
        maxWidth: 25,
        name: L('PDF'),
        fieldName: 'pdfStatus',
        onRender: (item: any) => {
          const isGenerated = !!item.pdfStatus && item.pdfStatus === "Generated";
          return <Icon style={{color: isGenerated ? "green" : "red"}} iconName={isGenerated ? "SkypeCheck" : "StatusCircleErrorX"} />;
        }
      },
      {
        name: L('Insurer'),
        fieldName: 'insurer',
        minWidth: 100,
        maxWidth: 100,
      },
      {
        name: L('Policy number'),
        fieldName: 'policyNumber',
      },
      {
        name: L('Agent'),
        fieldName: 'Agent',
        minWidth: 100,
        maxWidth: 100,
        onRender: (item: any) => {
          return `${item.agent ? item.agent.fullName : ''}`;
        }
      },
      {
        name: L('Offer number'),
        fieldName: 'offerNumber',
        onRender: (item: any): any => {
          return <Link onClick={(e) => {
                        e.preventDefault();
                        props.history.push(`/${RouterPath.InsurancePolicy}/${item.id}`);
                      }} 
                        href={`/${RouterPath.InsurancePolicy}/${item.id}`}>
                  {item.offerNumber}
                </Link>
        }
      },
      {
        name: L('Calculation Id'),
        fieldName: 'calculationId',
        minWidth: 80,
        maxWidth: 80,
      },
      {
        name: L('Segment'),
        fieldName: 'segment',
        minWidth: 120,
        maxWidth: 120,
        onRender: (item: any) => {
          return L(`${item.segment}2`);
        }
      },
      {
        name: L('Client name'),
        fieldName: 'clientFullName',
        minWidth: 180,
        maxWidth: 180,
        // onRender: (item: any) => {
        //   if(item.customerName && item.customerSurname) {
        //     return `${item.customerName} ${item.customerSurname}`;
        //   } else {
        //     return '';
        //   }
        // }
      },
      // {
      //   name: L('Customer surname'),
      //   fieldName: 'customerSurname',
      //   minWidth: 110,
      //   maxWidth: 110,
      // },
      {
        minWidth: 25,
        maxWidth: 25,
        name: L('APK'),
        fieldName: 'isApkSinged',
        onRender: (item: any) => {
          return <Icon style={!!item.isApkSinged && item.isApkSinged === true ? {color: "green"} : {color: "red"}} 
                      iconName={ !!item.isApkSinged && item.isApkSinged === true ? "SkypeCheck" : "StatusCircleErrorX" } />
        }
      },
      {
        name: L('Status'),
        fieldName: 'status',
        minWidth: 100,
        maxWidth: 100,
        onRender: (item: InsurancePolicyDto) => {
          let Color = additionalTheme.white;
          let Background = myTheme.palette.orange;

          if (item.status === 'Accepted' || 'Policy issued') {
            Color = additionalTheme.white;
            Background = myTheme.palette.green;
          }  if (item.status === 'InProgress') {
            Color = additionalTheme.white;
            Background = myTheme.palette.orange;
          } if (item.status === 'Cancelled') {
            Color = additionalTheme.white;
            Background = additionalTheme.lighterRed;
          } if (item.status === 'Annulled') {
            Color = additionalTheme.white;
            Background = additionalTheme.lighterRed;
          }

          return <span style={{ color: Color, backgroundColor: Background, padding: '2px 5px', borderRadius: '2px' }}>
            {L(item.status)}
          </span>
        }
      },
      {
        name: L('Payment status'),
        fieldName: 'paymentStatus',
        minWidth: 100,
        maxWidth: 100,
        onRender: (item: InsurancePolicyDto) => {
          let Color = additionalTheme.white;
          let Background = myTheme.palette.orange;

          if (item.paymentStatus === 'Paid') {
            Color = additionalTheme.white;
            Background = myTheme.palette.green;
          } else if (item.paymentStatus === 'UnPaid') {
            Color = additionalTheme.white;
            Background = additionalTheme.lighterRed;
          }
          return <span style={{ color: Color, backgroundColor: Background, padding: '2px 5px', borderRadius: '2px' }}>
            {!!item.paymentStatus ? L(item.paymentStatus) : L('None')}
          </span>
        }
      },
      {
        name: L('Order date'),
        fieldName: 'orderDate',
        onRender: (item: InsurancePolicyDto) => {
          return item.orderDate ? dateFormat(item.orderDate, undefined, true) : ''
        }
      },
      {
        name: L('Creation time'),
        fieldName: 'creationTime',
        onRender: (item: InsurancePolicyDto) => {
          return item.creationTime ? dateFormat(item.creationTime, undefined, true) : ''
        }
      },
      {
        name: L('Start date'),
        fieldName: 'startDate',
        onRender: (item: InsurancePolicyDto) => {
          return item.startDate ? dateFormat(item.startDate, undefined, true) : ''
        }
      },
      {
        name: L('End date'),
        fieldName: 'endDate',
        onRender: (item: InsurancePolicyDto) => {
          return item.endDate ? dateFormat(item.endDate, undefined, true) : ''
        }
      },
      {
        name: L('Cancellation date'),
        fieldName: 'cancellationDate',
        minWidth: 180,
        maxWidth: 180,
        onRender: (item: InsurancePolicyDto) => {
          return item.cancellationDate && new Date(item.cancellationDate).getFullYear() >= 1900 ? dateFormat(item.cancellationDate, undefined, true) : '-';
        }
      },
    ];
  }

  getTitle(): string {
    return L('Insurance policy list');
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: false,
      update: true,
      delete: false,
      customActions: true,
    };
  }

  private async reloadItems() {
    this.selectionSetAllSelected(false);
    if(typeof this.props.refreshItems !== 'undefined') {
      await this.props.refreshItems!();
    } else {
      this.togglePopUpDialog("Error", "The list could not be refreshed automatically, please refresh the page manually.");
    }
  }

  private togglePopUpDialog(title: string, text: string, visibility?: boolean) {
    this.popUpDialogTitle = title;
    this.popUpDialogText = text;
    this.showPopUpDialog = true;
    this.forceUpdate();
  }

  private reloadListOnDialogClose() {
    this.showPopUpDialog = false;

    if(this.shouldReloadItems) {
      this.reloadItems();
    }

    this.forceUpdate();
  }

  private async refreshPolicyStatus(policy: any, that: any) {
    if(policy.id && policy.offerNumber && policy.insurer) {
      this.shouldReloadItems = false;

      await insurancePolicyService.getPolicy(policy.insurer, policy.offerNumber).then((response: any) => {
        if(!response.success && response.error) {
          this.togglePopUpDialog("Error", catchErrorMessage(Array.isArray(response.error) ? response.error[0] : response.error));
        } else {
          this.shouldReloadItems = true;
          this.togglePopUpDialog("Success", `${L('Policy status has been updated successfully!')}${response.data && response.data.result ? '\n\r'+ L('New status:') + ' ' + L(response.data.result.status) : ''}`);
        }
        
        that.toggleCustomActionButton(false, true);
      }).catch((error) => {
        console.error(catchErrorMessage(error));
        this.togglePopUpDialog("Error", catchErrorMessage(error));
        that.toggleCustomActionButton(false, true);
      });
    } else {
      this.togglePopUpDialog("Error", L("Insufficient data on this policy."));
      that.toggleCustomActionButton(false, true);
    }
  }

  private async makePolicy(policy: any, that: any) {
    if(policy.insurer !== 'Allianz' || (policy.insurer === 'Allianz' && this.showAcceptanceCodePopUpDialog)) {
      this.showAcceptanceCodePopUpDialog = false;
      this.forceUpdate();

      if(policy.id) {
        if(InsurancePolicyStatus.PolicyIssued !== policy.status) {
          if(InsurancePolicyStatus.Accepted === policy.status) {
            this.shouldReloadItems = false;
      
            if(policy.insurer === 'Allianz' && !!this.uwAcceptanceCode) {
              await policyFinalizationService.finalizeWithAcceptanceUw({'abpPolicyId': policy.id, 'acceptanceCodeUW': this.uwAcceptanceCode}).then((response: any) => {
                if(response && response.data && response.data.result && response.data.result.policy) {
                  let policy = response.data.result.policy;
                  
                  if(policy.success) {
                    this.shouldReloadItems = true;
                    this.togglePopUpDialog("Success", `${L('Policy finalization success!')}`);
                  } else if(policy.errors && policy.errors.length > 0) {
                    let text = "";
        
                    policy.errors.forEach((error: string) => {
                      text += error;
                      if(policy.errors.length > 1) {
                          text += "\n\r\n\r";
                      }
                    });
                    this.togglePopUpDialog("Error", text);
                  } else {
                    this.togglePopUpDialog("Error", L('Not enough data available.'));
                  }
                } else {
                  this.togglePopUpDialog("Error", L('Response from server is not valid.'));
                }
                
                that.toggleCustomActionButton(false, true);
              }).catch((error) => {
                console.error(catchErrorMessage(error));
                this.togglePopUpDialog("Error", catchErrorMessage(error));
                that.toggleCustomActionButton(false, true);
              });
            } else {
              await policyFinalizationService.finalize({'abpPolicyId': policy.id}).then((response: any) => {
                if(response && response.data && response.data.result && response.data.result.policy) {
                  let policy = response.data.result.policy;
                  
                  if(policy.success) {
                    this.shouldReloadItems = true;
                    this.togglePopUpDialog("Success", `${L('Policy finalization success!')}`);
                  } else if(policy.errors && policy.errors.length > 0) {
                    let text = "";
        
                    policy.errors.forEach((error: string) => {
                      text += error;
                      if(policy.errors.length > 1) {
                          text += "\n\r\n\r";
                      }
                    });
                    this.togglePopUpDialog("Error", text);
                  } else {
                    this.togglePopUpDialog("Error", L('Not enough data available.'));
                  }
                } else {
                  this.togglePopUpDialog("Error", L('Response from server is not valid.'));
                }
                
                that.toggleCustomActionButton(false, true);
              }).catch((error) => {
                console.error(catchErrorMessage(error));
                this.togglePopUpDialog("Error", catchErrorMessage(error));
                that.toggleCustomActionButton(false, true);
              });
            }
          } else {
            this.togglePopUpDialog("Error", L("Incorrect status of the insurance policy to carry out this action."));
            that.toggleCustomActionButton(false, true);
          }
        } else {
          this.togglePopUpDialog("Error", L("The insurance policy has already been finalized."));
          that.toggleCustomActionButton(false, true);
        }
      } else {
        this.togglePopUpDialog("Error", L("Insufficient data on this policy."));
        that.toggleCustomActionButton(false, true);
      }
    } else if(policy.insurer === 'Allianz') {
      this.saveMakePolicyProps.policy = policy;
      this.saveMakePolicyProps.that = that;

      this.showAcceptanceCodePopUpDialog = true;
      this.forceUpdate();
    }
  }

  private async getInsuranceListLink(policy: any, that: any) {
    if(policy && policy.insurer && policy.offerNumber) {
      await insurancePolicyService.GetPolicyUrl(policy.insurer, policy.offerNumber).then((response: any) => {
        if(!response.data.success && response.data.error) {
          this.togglePopUpDialog("Error", catchErrorMessage(Array.isArray(response.data.error) ? response.data.error[0] : response.data.error));
        } else {
          let link: any = document.createElement("a");
          link.href = response.data.result;
          link.target = '_blank';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          link = null;
        }

        that.toggleCustomActionButton(false, true);
      }).catch((error: any) => {
        console.error(catchErrorMessage(error));
        this.togglePopUpDialog("Error", catchErrorMessage(error));
        that.toggleCustomActionButton(false, true);
      });
    } else {
      this.togglePopUpDialog("Error", L("Insufficient data on this policy."));
      that.toggleCustomActionButton(false, true);
    }
  }

  private async downloadPolicyDocuments(policy: any, that: any) {
    if(policy && !!policy.id && policy.id > 0) {
      if(policy.pdfStatus !== "Generated" && !!policy.policyNumber && policy.status === InsurancePolicyStatus.PolicyIssued) {
        await insurancePolicyService.DownloadPolicyDocuments(policy.id).then((response: any) => {
          if(!response.success && response.error) {
            this.togglePopUpDialog("Error", catchErrorMessage(Array.isArray(response.error) ? response.error[0] : response.error));
          } else {
            let link: any = document.createElement("a");
            link.download = response.result.policyPdfs[0].fileName;
            link.href = response.result.policyPdfs[0].url;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            link = null;
          }
  
          that.toggleCustomActionButton(false, true);
        }).catch((error: any) => {
          console.error(catchErrorMessage(error));
          this.togglePopUpDialog("Error", catchErrorMessage(error));
          that.toggleCustomActionButton(false, true);
        });
      } else {
        this.togglePopUpDialog("Error", L("PDF for this policy is already generated. Please check policy details."));
        that.toggleCustomActionButton(false, true);
      }
    } else {
      this.togglePopUpDialog("Error", L("Insufficient data on this policy."));
      that.toggleCustomActionButton(false, true);
    }
    this.forceUpdate()
  }

  getCommandBarBaseProps() {
    let props = super.getCommandBarBaseProps();
    return {
      ...props,
      setButtons: () => {
        return {
          'newItem': {text: 'New', icon: 'Add'},
          'delete': {text: 'Delete', icon: 'Delete'},
          'edit': {text: 'Details'},
          'newMsg': {text: 'Msg', icon: ''},
        }
      },
      customActionsProps: [{
        displayFor: 'single',
        buttonText: L("Refresh status"),
        buttonIcon: "none",
      },
      // {
      //   displayFor: 'single',
      //   buttonText: L("Make policy"),
      //   buttonIcon: "none",
      //   buttonColor: myTheme.palette.black,
      //   buttonIconColor: myTheme.palette.white,
      //   buttonBackground: myTheme.palette.white,
      // },
      {
        displayFor: 'single',
        buttonText: L("Generate and download documents"),
        buttonIcon: "Download",
        buttonColor: myTheme.palette.black,
        buttonIconColor: myTheme.palette.black,
        buttonBackground: myTheme.palette.white,
      }],
      customActions: [
        async (policy: InsurancePolicyDto, that: any) => {
          that.toggleCustomActionButton(true, true);
          await this.refreshPolicyStatus(policy, that);
        },
        // async (policy: InsurancePolicyDto, that: any) => {
        //   that.toggleCustomActionButton(true, true);
        //   await this.makePolicy(policy, that);
        // },
        async (policy: InsurancePolicyDto, that: any) => {
          that.toggleCustomActionButton(true, true);
          await this.downloadPolicyDocuments(policy, that);
        }
      ],
    }
  }

  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <>
      <Dialog
        hidden={!this.showPopUpDialog}
        onDismiss={() => this.reloadListOnDialogClose()}
        dialogContentProps={{
            type: DialogType.normal,
            title: L(this.popUpDialogTitle),
            subText: L(this.popUpDialogText),
        }}
        modalProps={{
            isBlocking: true
        }}
      >
      </Dialog>

      <Dialog
        hidden={!this.showAcceptanceCodePopUpDialog}
        onDismiss={() => { this.showAcceptanceCodePopUpDialog = false; this.saveMakePolicyProps.that.toggleCustomActionButton(false, true); this.forceUpdate(); }}
        dialogContentProps={{
            type: DialogType.normal,
            // title: L(this.popUpDialogTitle),
            // subText: L(this.popUpDialogText),
        }}
        modalProps={{
          isBlocking: true,
        }}
        minWidth='550px'
        maxWidth='550px'
      >
        <LabeledTextField key={'uwAcceptanceCode'} required={false} 
            label={L('Underwriter acceptance code')} 
            // errorMessage={error && error[element.id] ? error[element.id] : ''}
            value={this.uwAcceptanceCode}
            disabled={false} isDataLoaded={true} 
            onChange={(e: any) => {
                this.uwAcceptanceCode = (e.target && e.target.value ? e.target.value : (typeof e === 'string' ? e : ''));
                this.forceUpdate();
            }} 
        />

        <DialogFooter theme={myTheme}>
          <DefaultButton theme={myTheme} 
            onClick={() => { this.showAcceptanceCodePopUpDialog = false; this.saveMakePolicyProps.that.toggleCustomActionButton(false, true); this.forceUpdate(); }} 
            text={L('Cancel')}
          />
          <PrimaryButton
              text={L("Make policy")}
              theme={myTheme}
              disabled={false}
              onClick={() => {
                this.makePolicy(this.saveMakePolicyProps.policy, this.saveMakePolicyProps.that);
              }}
          />
        </DialogFooter>
      </Dialog>

      <InsurancePolicyPanel
        {...props}
      />
    </>
  }
}