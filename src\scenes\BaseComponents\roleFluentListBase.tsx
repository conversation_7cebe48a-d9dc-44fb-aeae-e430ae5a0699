import { ConstrainMode, Icon, SearchBox, ThemeProvider } from "@fluentui/react";
import { L } from "../../lib/abpUtility";
import { fluentTableClassNames } from "../../styles/fluentTableStyles";
import {additionalTheme, myTheme} from "../../styles/theme";
import { ICrudPermissons } from "./commandBarBase";
import { ITableColumn } from "./ITableColumn";
import { LabelComponent } from "./labelComponent";
import { LabelContainerComponent } from "./labelContainerComponent";
import * as React from 'react';
import { DetailsListLayoutMode, Selection, IColumn, IDetailsHeaderProps, IDetailsColumnRenderTooltipProps, IDetailsListProps, IDetailsRowStyles, DetailsRow, SelectionMode, Spinner, SpinnerSize } from '@fluentui/react';
import { getId, MarqueeSelection, ScrollablePane, ScrollbarVisibility, Sticky, StickyPositionType, IRenderFunction, TooltipHost, mergeStyleSets } from '@fluentui/react';
import { Announced } from '@fluentui/react';
import { EntityStringDto, NamedEntityStringDto } from '../../services/dto/entityStringDto';
import { Dialog, DialogType, DialogFooter } from '@fluentui/react';
import { PrimaryButton, DefaultButton } from '@fluentui/react';
import { CommandBarBase, ICommandBarBaseProps } from '../BaseComponents/commandBarBase';
import { CrudConsts } from '../../stores/crudStoreBase';
import { ShimmeredDetailsList } from '@fluentui/react';
import { Container } from '../../stores/storeInitializer';
import { asyncForEach, catchErrorMessage, isJsonString } from '../../utils/utils';
import { ITableState } from '../BaseComponents/ITableState';
import { utilMapToColumn } from '../../utils/tableUtils';
import { getPartialModel } from '../../utils/modelUtils';
import { gnInputIconStyles } from "../../styles/gnInputIconStyles";
import AppConsts from "../../lib/appconst";

var _ = require('lodash');

const classNames = mergeStyleSets({
    headerWrapper: {
        display: 'flex',
        justifyContent: 'space-between',
        padding: '20px 3% 0',
    },
    selectionDetails: {
        minHeight: '30px',
        height: 'auto',
        padding: '5px 3% 5px 60px',
        selectors: {
            '& > div': {
                height: 'auto',
                width: '100%',
            }
        }
    },
    rowStyle: {
        backgroundColor: additionalTheme.white
    },
    tableWrapper: {
        color: myTheme.palette.neutralDark,
        selectors: {
            '& .ms.List-Cell': {
                backgroundColor: additionalTheme.white
            },
            '& .ms-DetailsHeader': {
                paddingTop: 0,
                textTransform: 'uppercase',

            },
            '& .ms-DetailsHeader-cell.ms-DetailsHeader-cellIsCheck .is-checked.ms-Check:before': {
                background: myTheme.palette.themePrimary,
            },
        }
    },
    checkboxCell: {
        selectors: {
            '& .is-checked.ms-Check:before': {
                background: myTheme.palette.themePrimary,
            },
        }
    },
    paginationFooter: {
        width: "100%",
        height: 50,
        backgroundColor: "transparent",
    },
    smallLoadSpinner: {
        position: 'absolute',
        top: '100px',
        left: 0,
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    smallLoadSpinnerLower: {
        top: '5px',
        left: '10px',
    },
    smallLoadSpinnerLowerWithLabel: {
        top: '40px',
        left: '10px',
    },
    customScrollablePane: {
        selectors: {
            '& .ms-DetailsList.is-horizontalConstrained': {
                overflowX: 'unset', // Only disable horizontal overflow, keep vertical scrolling
                overflowY: 'auto',  // Ensure vertical scrolling is enabled
            }
        }
    }
});

export abstract class RoleFluentListBase<TDto extends EntityStringDto, State extends ITableState<TDto> = ITableState<TDto>> extends React.Component<any, State> {
    private _selection: Selection;
    private _labelId: string = getId('dialogLabel');
    private _subTextId: string = getId('subTextLabel');
    protected _footerRef: React.RefObject<HTMLDivElement>;
    protected disableGetAllOnMount: boolean = false;

    constructor(props: any) {
        super(props);

        if (props.customSelection) {
            this._selection = props.customSelection;
        } else {
            this._selection = new Selection({
                selectionMode: this.hasAnyCrudPermission() ? (typeof this.props.selectionMode === 'number' ? this.props.selectionMode : SelectionMode.multiple) : SelectionMode.none,
                onSelectionChanged: () => {
                    if (props.customOnSelectionChanged) {
                        props.customOnSelectionChanged(this._selection.getSelection());
                    }
                    this.setState({ selectionDetails: this._getSelectionDetails() });
                },
            });
        }

        this._footerRef = React.createRef();

        this.state = {
            ...this.state,
            allItems: props.items,
            isModalSelection: false,
            isCompactMode: false,
            showModal: false,
            isDraggable: false,
            showDialog: false,
            dialogEntities: [0],
            dialogEntityId: 0,
            maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE,
            modalVisible: false,
            skipCount: 0,
            filter: undefined,
            selectionDetails: this._getSelectionDetails(),
            dialogAction: this.delete,
            dialogTitle: L('Do you want to delete these item?'),
            isBulkOperation: false,
            isBusy: false,
            isShimmerEnabled: props.isDataLoaded,
            asyncActionInProgress: false,
        };
    }

    private mapToColumn(tableColumns: ITableColumn[]) {
        return utilMapToColumn(tableColumns, this._onColumnClick);
    }

    async componentDidMount() {
        this._openDialogFromNavigation();

        if (this.props.history && this.props.history.location && !!this.props.history.location.pathname) {
            const scrollElement: any = document.querySelector(`#${this.props.history.location.pathname.replaceAll('/', '')}-additional-scrollbar`);
            const listScrollElement: any = document.querySelector('.ms-ScrollablePane--contentContainer');

            if (scrollElement && !!scrollElement) {
                scrollElement.addEventListener('scroll', (event: any) => {
                    listScrollElement?.scrollTo(event.srcElement.scrollLeft, listScrollElement.scrollTop);
                });
            }

            if (listScrollElement && !!listScrollElement) {
                listScrollElement.addEventListener('scroll', (event: any) => {
                    scrollElement.scrollTo(event.srcElement.scrollLeft, 0);
                });
            }
        }
    }

    componentWillUnmount() {
        if (this.props.history && this.props.history.location && !!this.props.history.location.pathname) {
            const scrollElement: any = document.querySelector(`#${this.props.history.location.pathname.replaceAll('/', '')}-additional-scrollbar`);
            const listScrollElement: any = document.querySelector('.ms-ScrollablePane--contentContainer');

            if (scrollElement && !!scrollElement) {
                scrollElement.removeEventListener('scroll', (event: any) => {
                    listScrollElement?.scrollTo(event.srcElement.scrollLeft, listScrollElement.scrollTop);
                });
            }

            if (listScrollElement && !!listScrollElement) {
                listScrollElement.removeEventListener('scroll', (event: any) => {
                    scrollElement.scrollTo(event.srcElement.scrollLeft, 0);
                });
            }
        }
    }

    private debouncedOnSearchboxChange: any = _.debounce((e: any, newValue: string | undefined, customPayload?: any) => {
        newValue = typeof newValue === 'undefined' || newValue.length === 0 ? " " : newValue;
        if(this.props.customOnSearchTextChanged) {
            this.props.customOnSearchTextChanged(newValue);
        } else {
            this.overrideFilter(newValue);
        }
    }, AppConsts.defaultSerachBarDelay, []);

    private _openDialogFromNavigation() {
        let urlParams = new URLSearchParams(window.location.search);
        let id = urlParams.get("id");
        if (id) {
            let numberId = id;
            if (numberId) {
                let index = this.state.allItems.findIndex(x => x.id === numberId);
                if (index !== -1)
                    this.createOrUpdateModalOpen({ id: numberId } as TDto);
            }
        }
    }

    protected selectionSetAllSelected(bool: boolean) {
        this._selection.setAllSelected(bool);
    }

    protected overrideAllItems(items: any[]) {
        if (items && Array.isArray(items)) {
            this.setState({ allItems: items });
        }
    }

    protected overrideFilter(text: string) {
        if (text)
            this.setState({ filter: text });
    }

    static getDerivedStateFromProps(props: any, state: any) {
        const objToUpdate: any = { ...state, items: props.items, isShimmerEnabled: props.isDataLoaded, allItems: props.items };
        if (props.searchText) {
            objToUpdate.filter = props.searchText;
        }

        return objToUpdate;
    }

    showDialog(input: TDto[], action?: (input: TDto, values?: any) => Promise<void>, title?: string) {
        this.setState({ dialogEntities: input, dialogAction: action ?? this.delete, dialogTitle: title ?? L('Do you want to delete these item?') });
        this._showDialog();
    }

    delete = async (input: EntityStringDto) => {
        this.setState({ asyncActionInProgress: true });
        this._selection.setAllSelected(false);
        const filteredEntityStringDto: any = getPartialModel(input, ['id']);
        await this.props.store.delete(filteredEntityStringDto as TDto);
        if (!this.state.isBulkOperation) {
            this.setState({ pagedAllItems: [], asyncActionInProgress: false });
        } else {
            this.setState({ asyncActionInProgress: false });
        }
    };

    update = async (element: TDto, values: any) => {
        this.setState({ asyncActionInProgress: true });
        // fill default values
        let defaultModel = this.props.store.model;
        Object.keys(element).forEach((property) => {
            element[property] = element[property] || defaultModel[property];
        });
        // set form values
        Object.keys(values).forEach((property) => {
            element[property] = values[property];
        });
        // send all
        await this.props.store.update({ ...element, id: element.id });
        await this.props.store.getUpdated([element.id]);
        this.setState({ asyncActionInProgress: false });
    }

    async bulkOperation(entities: TDto[], action?: (input: TDto, values?: any) => Promise<void>) {
        this.props.store.createDefault();
        this.setState({ dialogEntities: entities, dialogAction: action ?? this.update })
        this.Modal(true);
    }

    async createOrUpdateModalOpen(entityStringDto: TDto) {
        if (entityStringDto.id === '') {
            this.props.store.createDefault();
        } else {
            const filteredEntityStringDto: any = getPartialModel(entityStringDto, ['id']);
            await this.props.store.get(filteredEntityStringDto);
        }

        this.setState({ dialogEntityId: entityStringDto.id });
        this.Modal();
    }

    Modal = (isBulkOperation: boolean = false) => {
        Container.EventBus.customErrorHandling = true;
        this.setState({
            modalVisible: !this.state.modalVisible,
            isBulkOperation: isBulkOperation
        });
    };

    async createOrUpdateBulk(values: any) {
        let dtos = this.state.dialogEntities;
        dtos.forEach(async element => {
            await this.state.dialogAction(element, values);
        });

        this.setState({ pagedAllItems: [] });
    }

    async createOrUpdate(values: any) {
        let result: any = true;

        if (this.state.isBulkOperation) {
            await this.createOrUpdateBulk(values);
        } else {
            if (this.state.dialogEntityId === '') {
                await this.props.store.create(values).then(async (response: any) => {
                    if (!!response && (!response.hasOwnProperty('result') || typeof response.result !== 'undefined')) {
                        if (response.id && response.id > 0) {
                            await this.props.store.getUpdated([response.id]);
                        } else {
                            this.setState({ pagedAllItems: [] });
                        }
                    } else {
                        result = false;
                    }
                }).catch((error: any) => {
                    result = catchErrorMessage(error);
                    console.error(result);
                });
            } else {
                if (this.props.store.crudService) {
                    await this.props.store.crudService.update({ id: this.state.dialogEntityId, ...values }).then(async (response: any) => {
                        if (!!response && (!response.hasOwnProperty('result') || typeof response.result !== 'undefined')) {
                            await this.props.store.getUpdated([this.state.dialogEntityId]);
                        } else {
                            result = false;
                        }
                    }).catch((error: any) => {
                        result = catchErrorMessage(error);
                        console.error(result);
                    });
                } else {
                    await this.props.store.update({ id: this.state.dialogEntityId, ...values }).then(async (response: any) => {
                        await this.props.store.getUpdated([this.state.dialogEntityId]);
                    }).catch((error: any) => {
                        result = catchErrorMessage(error);
                        console.error(result);
                    });
                }
            }
        }

        return result;
    }

    closeModal(calledFromButton: boolean) {
        if (Container.EventBus.HttpError && !calledFromButton) {
            return;
        }
        Container.EventBus.customErrorHandling = false;
        this.setState({ modalVisible: false });
    }

    async onDialogYesClick() {
        try {
            if (!this.state.isBusy) {
                this.setState({ isBusy: true });
                let isClosed = await this._closeDialogAsync();
                if (isClosed) {
                    this.setState({ dialogYesButtonDisabled: true }, async () => {
                        await asyncForEach<TDto>(this.state.dialogEntities, async (x) => {
                            await this.state.dialogAction(x);
                        });
                        isClosed = await this._closeDialogAsync();
                        if (isClosed) this.setState({ dialogYesButtonDisabled: false });
                    });
                }
            }
        } catch (error) {
            console.error(error);
        } finally {
            this.setState({ isBusy: false });
        }
    }

    getItems() {
        return this.onFilter(this.state.pagedAllItems && this.state.pagedAllItems.length > 0 ? this.state.pagedAllItems : this.state.allItems, this.state.filter || '');
    }

    public render() {
        let filteredItems = this.getItems();
        let tableColumns = this.getColumns();
        let defaultColumns: IColumn[] = this.mapToColumn(tableColumns);

        let { columns, items } = this.orderBy(filteredItems, defaultColumns);
        const values: any = this.tranformValues(items);
        let pageInfo: string = "";


        return this.renderAll(pageInfo, values, columns);
    }

    renderAll(pageInfo: string, values: any, columns: any) {
        let icons: any = <></>;
        
        if(this.props.customData && this.props.customData.iconName) {
            if(!isJsonString(this.props.customData.iconName)) {
                icons = <Icon iconName={this.props.customData.iconName} id={`${this.props.id ? this.props.id + '_' : ''}${this.props.customData.iconName}`} className={gnInputIconStyles.icon} 
                            onClick={() => this.props.customData.iconOnClick!()} style={{alignSelf: 'flex-start', marginLeft: '5px', marginRight: '5px', marginTop: '5px'}}
                            title={this.props.customData.iconTitle ? this.props.customData.iconTitle : ''} />;
            } else {
                let parsedJsonString: any = JSON.parse(this.props.customData.iconName);
                if(parsedJsonString && Array.isArray(parsedJsonString)) {
                    icons = parsedJsonString.map((iconName: string, index: number) => {
                        return <Icon iconName={iconName} id={`${this.props.id ? this.props.id + '_' : ''}${this.props.customData.iconName}`} className={gnInputIconStyles.icon} 
                                    onClick={() => this.props.customData.iconOnClick!(index)} style={{alignSelf: 'flex-start', marginLeft: '5px', marginRight: '5px', marginTop: '5px'}}
                                    title={this.props.customData.iconTitle ? this.props.customData.iconTitle : ''} /> 
                    });
                }
            }
        }

        return (
            <>
                <LabelContainerComponent marginTop={"0"} flexWrap={'wrap'}>
                    {icons}
                    <LabelComponent label={L("Select permission")} />
                    <SearchBox theme={myTheme} placeholder={L("Search")}
                        styles={{
                            root: {
                                flex: 1,
                                maxWidth: "252px",
                                height: "32px",
                                backgroundColor: myTheme.palette.white,
                                border: `1px solid ${myTheme.palette.black}`,
                                boxSizing: "border-box",
                                marginBottom: '30px'
                            },
                            field: { borderRadius: "2px" },
                        }}
                        onChange={ (e: any, newValue: string | undefined) => {
                            this.debouncedOnSearchboxChange(e, newValue);
                        }}
                    />

                    {this.props.customData.selectedItems &&
                        typeof this.props.customData.selectedItems !== "undefined" && (
                            <div className={fluentTableClassNames.summaryAttributeWrapper} style={{overflowY: 'auto', maxHeight: '100px'}}>
                                {Array.isArray(this.props.customData.selectedItems) &&
                                    this.props.customData.selectedItems.map((item: any) => {
                                        return (
                                            <p className={fluentTableClassNames.summaryAttribute}>
                                                <span>
                                                    {item ? item : ""}
                                                </span>
                                                <div style={{backgroundColor: additionalTheme.white, borderRadius: '50%', width: '20px', height: '20px', display: 'flex', justifyContent: 'center', alignItems: 'center', marginLeft: '10px'}}>
                                                    <Icon iconName="Cancel" title={L("Delete")}
                                                        style={{
                                                        marginRight: '0', 
                                                        cursor: 'pointer', 
                                                        color: additionalTheme.lighterGrey,
                                                        fontWeight: 'bold'
                                                    }}
                                                        onClick={() => {
                                                            if (this.props.customOnSelectionChanged)
                                                                this.props.customOnSelectionChanged({
                                                                    action: "deletePermission",
                                                                    key: item,
                                                                });
                                                        }}
                                                    />
                                                </div>
                                            </p>
                                        );
                                    })}
                            </div>
                        )}
                </LabelContainerComponent>

                <ThemeProvider theme={myTheme}>
                    {this.renderAnnounced(pageInfo)}

                    {this.renderListScrollablePane(values, columns)}
                </ThemeProvider>
            </>
        );
    }

    renderListScrollablePane(values: any, columns: any) {
        return <ScrollablePane style={{ marginTop: !!this.props.scrollablePanelMarginTop ? this.props.scrollablePanelMarginTop : 145 }}
            scrollbarVisibility={ScrollbarVisibility.auto} theme={myTheme} className={classNames.customScrollablePane}
        >
            <MarqueeSelection selection={this._selection} isEnabled={false}>
                {this.renderList(values, this.state.isCompactMode, columns, this.state.isShimmerEnabled, classNames, this._selection, this.onItemInvoked)}
            </MarqueeSelection>

            <div className={classNames.paginationFooter} ref={this._footerRef} />
        </ScrollablePane>;
    }

    renderAnnounced(pageInfo: string) {
        return <Sticky stickyPosition={StickyPositionType.Header}>
            {this.props.history && this.props.history.location && !!this.props.history.location.pathname &&
                <div id={`${this.props.history.location.pathname.replaceAll('/', '')}-additional-scrollbar`} style={{ width: '100%', height: '17px', overflowX: 'scroll' }}>
                    <div style={{
                        width: `${document.querySelector('.ms-DetailsList-contentWrapper')?.querySelector('.ms-SelectionZone')?.querySelector('.ms-FocusZone')?.getBoundingClientRect().width}px`,
                        height: '17px'
                    }}>
                    </div>
                </div>
            }

            <Announced className={classNames.selectionDetails} message={this.state.selectionDetails + pageInfo} />

            {this.state.asyncActionInProgress && (
                <Spinner label={''} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="bottom"
                    className={`${classNames.smallLoadSpinner} 
                        ${this.props.customData && this.props.customData.lowerSpinner ? classNames.smallLoadSpinnerLower : ''}
                        ${this.props.customData && this.props.customData.lowerSpinnerWithLabel ? classNames.smallLoadSpinnerLowerWithLabel : ''}`}
                />
            )}
        </Sticky>;
    }

    renderDialog() {
        return <Dialog
            hidden={!this.state.showDialog}
            onDismiss={this._closeDialog}
            dialogContentProps={{
                type: DialogType.normal,
                title: this.state.dialogTitle,
                closeButtonAriaLabel: L('Close'),
            }}
            modalProps={{
                titleAriaId: this._labelId,
                subtitleAriaId: this._subTextId,
                isBlocking: false,
                styles: { main: { maxWidth: 450 } },
            }}
            theme={myTheme}
        >
            <DialogFooter theme={myTheme}>
                <DefaultButton theme={myTheme} onClick={this._closeDialog} text={L('No')} />
                <PrimaryButton
                    onClick={async () => await this.onDialogYesClick()}
                    text={L('Yes')}
                    theme={myTheme}
                    disabled={this.state.dialogYesButtonDisabled}
                />
            </DialogFooter>
        </Dialog>;
    }

    renderList(values: any[], isCompactMode: boolean, columns: IColumn[], isDataLoaded: boolean, classNames: any, selection: any, onItemInvoked: any) {
        return <ShimmeredDetailsList
                constrainMode={ConstrainMode.unconstrained}
                items={values}
                compact={isCompactMode}
                columns={columns}
                onRenderDetailsHeader={this.onRenderDetailsHeader}
                selection={selection}
                selectionMode={selection.mode}
                enableShimmer={typeof isDataLoaded === 'undefined' ? false : !isDataLoaded}
                setKey="none"
                layoutMode={DetailsListLayoutMode.justified}
                isHeaderVisible={true}
                checkButtonAriaLabel="Row checkbox"
                onItemInvoked={onItemInvoked}
                className={classNames.tableWrapper}
                theme={myTheme}
                checkboxCellClassName={classNames.checkboxCell}
                onRenderRow={this._onRenderRow}
            />;
    }

    renderCommandBar(props: ICommandBarBaseProps<TDto>): JSX.Element {
        return <CommandBarBase<TDto> {...props} />;
    }

    renderCommandBarBase(): JSX.Element {
        return <Sticky stickyPosition={StickyPositionType.Header}>
            {this.renderCommandBar({ ...this.getCommandBarBaseProps() })}
        </Sticky>;
    }

    renderTitle(): JSX.Element {
        return (
            <div className={classNames.headerWrapper}>
                <h2>{this.getTitle()}</h2>
            </div>
        );
    }

    onRenderDetailsHeader: IRenderFunction<IDetailsHeaderProps> = (props, defaultRender) => {
        if (!props) {
            return null;
        }

        const onRenderColumnHeaderTooltip: IRenderFunction<IDetailsColumnRenderTooltipProps> = (tooltipHostProps) => (
            <TooltipHost theme={myTheme} {...tooltipHostProps} />
        );

        return (
            <Sticky stickyClassName={classNames.tableWrapper} stickyPosition={StickyPositionType.Header} isScrollSynced={true}>
                {defaultRender!({
                    ...props,
                    onRenderColumnHeaderTooltip,
                })}
            </Sticky>
        );
    };

    private _onRenderRow: IDetailsListProps['onRenderRow'] = props => {
        const customStyles: Partial<IDetailsRowStyles> = {};
        if (props) {
            // if (props.itemIndex % 2 === 0) {
            //     // Every other row renders with a different background color
            //     // customStyles.root = { backgroundColor: additionalTheme.white };
            // }
            return <DetailsRow className={classNames.rowStyle} {...props} styles={customStyles} />;
        }
        return null;
    };

    getItemDisplayNameOf(item: TDto): string {
        let namedItem = item as any as NamedEntityStringDto;
        return namedItem && !!namedItem.name ? namedItem.name : "";
    }

    getTitle(): string {
        return ''
    }

    getItemFilterString(item: TDto): string {
        let array: string[] = [];
        this.getColumns().forEach((x) => {
            let fieldName = x.fieldName;
            let splittedFieldName: string[] = fieldName.split('.');

            if (splittedFieldName.length > 1) {
                let deeperModelValue = [];
                deeperModelValue[0] = item;
                for (let i = 1; i <= splittedFieldName.length; i++) {
                    if (deeperModelValue[i - 1] && splittedFieldName[i - 1] && deeperModelValue[i - 1][splittedFieldName[i - 1]]) {
                        deeperModelValue[i] = deeperModelValue[i - 1][splittedFieldName[i - 1]];
                    } else {
                        break;
                    }
                }

                if (!!deeperModelValue[deeperModelValue.length - 1] && deeperModelValue[deeperModelValue.length - 1].length > 0) {
                    array.push(deeperModelValue[deeperModelValue.length - 1]);
                }
            } else {
                let property = item[fieldName];
                if (!!property && property.length > 0) {
                    array.push(property);
                }
            }
        });

        return array.join();
    }

    getPayload(): any {
        return { model: this.props.store.model, entityId: this.state.dialogEntityId }
    }

    onFilter(allItems: TDto[], search: string): TDto[] {
        return allItems.filter((i) => this.getItemFilterString(i).toString().toLowerCase().indexOf(search.toLowerCase().trim()) > -1);
    }

    tranformValues(items: TDto[]): any[] {
        const array: any[] = [];
        items.forEach((item) => {
            array.push(this.transformToDisplayValues(item));
        });
        return array;
    }

    transformToDisplayValues(row: TDto): any {
        return row;
    }

    private _getSelectionDetails(): string {
        if (this.hasAnyCrudPermission()) {
            const selectionCount = this._selection.getSelectedCount();

            switch (selectionCount) {
                case 0:
                    return L('No items selected');
                case 1:
                    return L('1 item selected') + ': ' + this.getItemDisplayNameOf(this._selection.getSelection()[0] as TDto);
                default:
                    return `${selectionCount} ${L('items selected')}`;
            }
        } else {
            return '';
        }
    }

    getCommandBarBaseProps() {
        return {
            selection: this._selection,
            create: () => this.createOrUpdateModalOpen({ id: '' } as TDto),
            update: (item: TDto) => this.createOrUpdateModalOpen(item),
            delete: (item: TDto) => this.showDialog([item]),
            deleteMany: (items: TDto[]) => this.showDialog(items),
            updateMany: (items: TDto[]) => this.bulkOperation(items),
            permissons: this.getCrudPermission(),
            //more buttons
        };
    }

    hasAnyCrudPermission(): boolean {
        let premissions = this.getCrudPermission();
        if (premissions.create || premissions.update || premissions.delete || premissions.customActions) {
            return true;
        } else {
            return false;
        }
    }

    private _showDialog = (): void => {
        this.setState({ showDialog: true });
    };

    private _closeDialog = (): void => {
        this.setState({ showDialog: false });
    };

    private async _closeDialogAsync(): Promise<boolean> {
        let closeDialogPromise = new Promise<boolean>(resolve => {
            this.setState({ showDialog: false }, () => {
                resolve(true);
            })
        });
        return await closeDialogPromise;
    }

    onItemInvoked = (item: any): void => {
        let premissions = this.getCrudPermission()
        if (premissions.update)
            this.createOrUpdateModalOpen({ id: item.id } as TDto);
    }

    orderBy(items: TDto[], columns: IColumn[]) {
        const { column } = this.state;
        if (!column) {
            return {
                columns,
                items
            }
        }

        const newColumns: IColumn[] = columns.slice();
        for (let i = 0; i < newColumns.length; i++) {
            if (newColumns[i].key === column.key) {
                newColumns[i].isSortedDescending = !column.isSortedDescending;
                newColumns[i].isSorted = true;
            } else {
                newColumns[i].isSorted = false;
                newColumns[i].isSortedDescending = true;
            }
        }

        const newItems = this.copyAndSort(items, column.fieldName!, column.isSortedDescending);

        return {
            columns: newColumns,
            items: newItems,
        }
    }

    private _onColumnClick = (ev: React.MouseEvent<HTMLElement>, newCol: IColumn): void => {
        const { column } = this.state;
        if (column && newCol.key === column.key) {
            newCol.isSortedDescending = !column.isSortedDescending;
        }
        this.setState({
            column: newCol
        });
    };

    getSelection(): Selection {
        return this._selection;
    }

    getColumns(): ITableColumn[] {
        return this.getTableColumns(this.props);
    }

    private getTableColumns(props: any): ITableColumn[] {
        return [
            {
                name: L("Name"),
                fieldName: "text",
            },
        ];
    }

    getCrudPermission(): ICrudPermissons {
        return {
            create: false,
            update: false,
            delete: false,
            customActions: false,
        };
    }

    copyAndSort<T>(items: T[], columnKey: string, isSortedDescending?: boolean): T[] {
        const key = columnKey as keyof T;
        return items.slice(0).sort((a: T, b: T) => ((isSortedDescending ? a[key] < b[key] : a[key] > b[key]) ? 1 : -1));
    }
}