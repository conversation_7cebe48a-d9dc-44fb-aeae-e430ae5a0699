import { ClientDto } from "../client/dto/clientDto";
import { BaseApiEntityModel } from "../dto/BaseApiEntityModel";

export interface VehicleConfigDto extends BaseApiEntityModel {
    clientId: number,
    client: ClientDto,
    vin: string,
    firstRegistrationDate: string,
    registrationNumber: string,
    mileage: number,
    type: string,
    brand: string,
    year: string,
    fuelType: string,
    engineCapacity: string,
    model: string,
    enginePower: string,
    eurotaxCarId: string,
    infoExpertId: string,
    vehicleInfo: string,
    lastModificationTime: string,
    lastModifierUserId: number,
    creationTime: string,
    creatorUserId: number,
    dmc: number;
    netWeight: number;
    capacity: number;
}