import { IChoiceGroupOption, IComboBoxOption, Icon, IDropdownOption, mergeStyleSets, MessageBar, MessageBarType, Pivot, PivotItem, PrimaryButton, Spinner, SpinnerSize, Stack, Selection, SelectionMode } from '@fluentui/react';
import { L, isGranted } from '../../../lib/abpUtility';
import { Controls } from '../../BaseComponents/controls';
import { ContentViewModelProperty } from '../../BaseComponents/contentViewBase';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import {additionalTheme, myTheme} from '../../../styles/theme';
import { inject } from 'mobx-react';
import Stores from '../../../stores/storeIdentifier';
import gusService from "../../../services/gus/gusService";
import { catchErrorMessage, dateFormat, enumToChoiceGroupOptions, enumToDropdownOptions, filterBySome, isJsonString, modifyFirstLetter } from '../../../utils/utils';
import { ChoiceGroupBase } from '../../BaseComponents/ChoiceGroupBase';
import { ClientTypeEnum } from '../../../services/client/clientTypeEnums';
import { ClientDto } from '../../../services/client/dto/clientDto';
import { defaultClient } from '../../../stores/clientStore';
import { validateEmail, validateNip, validatePesel, validateRegon } from '../../../utils/inputUtils';
import { CountriesComboBox } from '../../BaseComponents/CountriesComboBox';
import { NationalityComboBox } from '../../BaseComponents/NationalityComboBox';
import React from 'react';
import { CheckBoxBase, DropdownBase, TextFieldBase } from '../../BaseComponents';
import clientAttachedFilesService from '../../../services/attachedFiles/clientAttachedFilesService';
import { ClientAttachedFilesDto } from '../../../services/attachedFiles/clientAttachedFilesDto';
import { ClientAttachedFileStatus } from '../../../services/attachedFiles/enums/clientAttachedFileStatusEnums';
import clientService from '../../../services/client/clientService';
import apkAttachedFilesService from '../../../services/apkAttachedFiles/apkAttachedFilesService';
import policyCalculationAttachedFilesService from '../../../services/attachedFiles/policyCalculationAttachedFilesService';
import { isConfigForAG, isConfigForProduction } from '../../../utils/authUtils';
import accountService from '../../../services/account/accountService';
import userService from '../../../services/user/userCrudService';
import { UserDto } from '../../../services/user/dto/userDto';
import { UserFluentListBase } from '../../BaseComponents/userFluentListBase';
import { fluentTableClassNames } from '../../../styles/fluentTableStyles';
import { defaultUser } from '../../../stores/userCrudStore';
import { ClientStructureDto } from '../../../services/client/dto/clientStructureDto';
import { ClientMaritalStatusEnum } from '../../../services/client/clientMaritalStatusEnums';
import { CustomerContestTab } from './customerContestTab';
import { CustomerVehicleListTab } from './customerVehicleListTab';
import { CustomerClaimListTab } from './customerClaimListTab';
import { CustomerCalculationsTab } from './customerCalculationsTab';
import { CustomerApkTab } from './customerApkTab';
import { CustomerPoliciesTab } from './customerPoliciesTab';
import { CustomerConsentsTab } from './customerConsentsTab';
import { ClientOccupationEnum } from '../../../services/client/clientOccupationEnums';

const classNames = mergeStyleSets({
    inputIcon: {
        cursor: 'pointer',
        marginLeft: '15px !important',
        marginRight: '10px',
        fontSize: '20px',
        marginTop: '6px',
        transition: 'all 120ms',
        selectors: {
            '&:hover': {
                transform: 'scale(1.2)',
            }
        }
    },
    smallLoadSpinner: {
        display: 'inline-flex',
        marginLeft: '10px !important',
        marginTop: '22px',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    uploadButton: {
        width: 'fit-content',
        padding: '25px 50px',
        marginTop: '20px',
    },
    attachedFilesLabel: {
        fontWeight: 'bold',
        marginTop: '20px',
        marginBottom: '10px',
    },
    attachedFilesListItem: {
        listStyleType: 'none',
        marginBottom: '15px',
        padding: '25px',
        // background: myTheme.palette.themeLighterAlt,
        border: `1px solid ${myTheme.palette.neutralQuaternary}`,
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        selectors: {
            // ':nth-child(even)': {
            //     background: myTheme.palette.neutralLight,
            // }
        }
    },
    attachedFilesListGroupedWrapper: {
        listStyleType: 'none',
        marginBottom: '15px',
        padding: '25px 25px 10px',
        // background: myTheme.palette.themeLighterAlt,
        border: `1px solid ${myTheme.palette.neutralQuaternary}`,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        selectors: {
            // ':nth-child(even)': {
            //     background: myTheme.palette.neutralLight,
            // }
        }
    },
    attachedFilesListGroupedItem: {
        width: '100%',
        minHeight: '40px',
        marginBottom: '15px',
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        selectors: {
            // ':nth-child(even)': {
            //     background: myTheme.palette.neutralLight,
            // }
        }
    },
    loadSpinner: {
        display: 'inline-flex',
        marginLeft: '45px !important',
        marginTop: '20px',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    fileLabelSmallLoadSpinner: {
        display: 'inline-flex',
        marginLeft: '30px !important',
        marginTop: '0',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
                textDecoration: 'none',
            }
        },
    },
    fileStatusDropdown: {
        marginTop: '-20px',
    },
    messageBar: {
        width: 'fit-content',
        marginLeft: '25px !important',
        marginTop: '20px',
        selectors: {
            '& .ms-MessageBar-innerText': {
                selectors: {
                    '& span': {
                        whiteSpace: 'pre-line',
                    }
                }
            }
        }
    },
    fileListLabel: {
        fontSize: 16,
        fontWeight: 'bold',
        textDecoration: 'underline',
    },
    filePasswordInput: {
        marginTop: 0,
        marginLeft: 25,
        border: '1px solid #000',
        height: '36px',
        letterSpacing: '3px',
    }
});

declare var abp: any;

export interface SelectedConsentCheckboxes {
    MandatoryAgreement: boolean;
    CommercialAgreement: boolean;
}

@inject(Stores.LanguageStore)
@inject(Stores.CountryStore)
@inject(Stores.UserCrudStore)
export class CustomerContentView extends GenericContentView {
    private firstStartFlag: boolean = true;
    private customer: ClientDto = defaultClient;
    private clientAttachedFiles: any = undefined;
    private clientHaveAgreements: {accepted: boolean, sentToClient: boolean, sendBySMS: boolean} = {accepted: false, sentToClient: false, sendBySMS: false};
    private allCountries: IDropdownOption[] = [];
    private countryOptions: any = {
        dropdown: [] as IDropdownOption[],
        choicegroup: [] as IChoiceGroupOption[]
    };
    private cityComboBoxOptions: IComboBoxOption[] = [];
    private allStreetComboBoxOptions: IComboBoxOption[] = [];
    private streetComboBoxOptions: IComboBoxOption[] = [];
    private prevGetCitiesResponse: any[] = [];
    private idOfPoland: string = "";
    private searchByNipOrRegonInProgress: string = '';
    private showAdditionalEmailInput: boolean = false;
    private showAdditionalPkdInput: boolean = false;
    private inputErrorsCount: number = 0;
    private fileUploadInputRef: any;
    private agreementsSentToClient: boolean = false;
    private agreementsSentToClientError: string = '';
    private agreementsSentToClientMessageBarType: MessageBarType = MessageBarType.error;
    private showClientFilePassword: boolean = false;
    private clientFilePassword: any = {};
    private clientFilePasswordError: string = '';
    private selectedFileForUpload: any = {
        name: "" as string,
        src: "" as string,
    };
    private selectedConsentCheckboxes: any = {
        MandatoryAgreement: true,
        CommercialAgreement: false,
    };
    private selectedFileStatus: any = {};
    private attachmentFileAsyncActionStatus: any = {};
    private fileStatusOptions: any = {
        dropdown: [
            {key: ClientAttachedFileStatus.Sended, text: L(ClientAttachedFileStatus.Sended), disabled: true},
            {key: ClientAttachedFileStatus.Signed, text: L(ClientAttachedFileStatus.Signed)},
            {key: ClientAttachedFileStatus.Rejected, text: L(ClientAttachedFileStatus.Rejected)},
        ] as IDropdownOption[],
    };
    private mobileAgreements: JSX.Element[] = [];
    private currentUser: any = {};
    private savedClientStructure: string = '';
    private clientStructureError: string = '';
    private superAgentManagerUser: UserDto = {...defaultUser};
    private superAgentDirectorUser: UserDto = {...defaultUser};
    private currentUserId: string = abp.session.userId;
    private creatorUserFullName: string = "";
    private selectedSuperAgentFullName: string = "";
    private _userListSuperAgentSelection: Selection = new Selection({
        onSelectionChanged: async () => {
            const selectedUser: any = this._userListSuperAgentSelection.getSelection();
            if(Array.isArray(selectedUser) && selectedUser.length > 0 && !!selectedUser[0].id) {
                this.asyncActionInProgress = true;
                this.forceUpdate();

                if(!!selectedUser[0].managerId && !!selectedUser[0].managerFullName) {
                    this.superAgentManagerUser['fullName'] = selectedUser[0].managerFullName;
                    this.superAgentManagerUser['id'] = selectedUser[0].managerId;
                } else if(!!selectedUser[0].managerId) {
                    this.superAgentManagerUser = await userService.get({ id: selectedUser[0].managerId } as UserDto);
                } else {
                    this.superAgentManagerUser = {...defaultUser};
                }

                if(!!selectedUser[0].directorId && !!selectedUser[0].directorFullName) {
                    this.superAgentDirectorUser['fullName'] = selectedUser[0].directorFullName;
                    this.superAgentDirectorUser['id'] = selectedUser[0].directorId;
                } else if(!!selectedUser[0].directorId) {
                    this.superAgentDirectorUser = await userService.get({ id: selectedUser[0].directorId } as UserDto);
                } else {
                    this.superAgentDirectorUser = {...defaultUser};
                }

                this.selectedSuperAgentFullName = `[${selectedUser[0].id}] ${selectedUser[0].name} ${selectedUser[0].surname}`;
                this.customer.superAgentId = selectedUser[0].id;
                this._userListSuperAgentSelection.setAllSelected(false);
                this.savedClientStructure = '';

                this.asyncActionInProgress = false;
            }
            this.forceUpdate();
        },
        selectionMode: SelectionMode.single
    });

    constructor(props: any) {
        super(props);
        this.fileUploadInputRef = React.createRef();
    }

    async componentDidMount() {
        this.asyncActionInProgress = true;
        this.forceUpdate();
        
        if(this.customer && !!this.customer.id) {
            await clientAttachedFilesService.checkIsClientHaveAgreements(this.customer.id).then((response: any) => {
                if(response && typeof response === 'object') {
                    this.clientHaveAgreements = response;
                }
            }).catch((error: any) => {
                console.error(error);
            });

            await clientService.getAttachedFiles(this.customer.id).then((response: any) => {
                if(response) {
                    this.clientAttachedFiles = response;
                }
            }).catch((error: any) => {
                console.error(error);
            });

            this.forceUpdate();
        }

        if(this.props.countryStore && (!this.props.countryStore.dataSet || this.props.countryStore.dataSet.items.length === 0)) {
            await this.props.countryStore.getAll(this.props.countryStore.defaultRequest);	
        }
        
        if(!!this.customer.creatorUserId) {
            const tempCreatorUser: UserDto = await userService.get({ id: this.customer.creatorUserId.toString() } as UserDto);
            this.creatorUserFullName = !!tempCreatorUser.fullName ? tempCreatorUser.fullName : `${tempCreatorUser.name} ${tempCreatorUser.surname}`;
        } else {
            this.currentUser = await userService.get({ id: this.currentUserId } as UserDto);
        }

        if(!!this.customer.superAgentId) {
            let tempSuperAgentUser: UserDto = await userService.get({ id: this.customer.superAgentId.toString() } as UserDto);
            this.selectedSuperAgentFullName = !!tempSuperAgentUser.fullName ? tempSuperAgentUser.fullName : `${tempSuperAgentUser.name} ${tempSuperAgentUser.surname}`;

            if(!!tempSuperAgentUser.managerId && !!tempSuperAgentUser.managerFullName) {
                this.superAgentManagerUser['fullName'] = tempSuperAgentUser.managerFullName;
            } else if(!!tempSuperAgentUser.managerId) {
                this.superAgentManagerUser = await userService.get({ id: tempSuperAgentUser.managerId.toString() } as UserDto);
            }

            if(!!tempSuperAgentUser.directorId && !!tempSuperAgentUser.directorFullName) {
                this.superAgentDirectorUser['fullName'] = tempSuperAgentUser.directorFullName;
            } else if(!!tempSuperAgentUser.directorId) {
                this.superAgentDirectorUser = await userService.get({ id: tempSuperAgentUser.directorId.toString() } as UserDto);
            }
        }

        this.checkIfDataIsLoaded("customer");

        if(this.customer && !!this.customer.agreementsPayload && isJsonString(this.customer.agreementsPayload)) {
            this.selectedConsentCheckboxes = { ...this.selectedConsentCheckboxes, ...JSON.parse(this.customer.agreementsPayload) };
        }

        if(!!this.customer.mainPKD && this.customer.mainPKD) {
            this.showAdditionalPkdInput = true;
        }

        if(!!this.customer.emailAdditional && this.customer.emailAdditional !== this.customer.user.emailAddress) {
            this.showAdditionalEmailInput = true;
        }

        if(!isConfigForAG() && !isConfigForProduction()) { // DEV or OLO_CLEVER
            await accountService.getMobileAgreements(this.customer.userId).then((response: any) => {
                if(response && Object.keys(response).length > 0) {
                    for(let agreementName in response) {
                        if(response.hasOwnProperty(agreementName)) {
                            this.mobileAgreements.push(
                                <CheckBoxBase label={L(`mobileAgreement-${agreementName}`)} 
                                    value={response[agreementName]} disabled={true}
                                    onChange={(e: any) => {return false}}
                                />
                            );
                        }
                    }
                }
            }).catch((error: any) => {
                console.error(error);
            });
        }

        if(this.customer.phone === '') {
            this.customer.phone = "+48";
        }

        if(!!this.customer.zipPostalCode) {
            this.zipPostalCodeValueChange(this.customer.zipPostalCode, this.customer.city, this.customer.streetAddress);
        }

        this.asyncActionInProgress = false;
        this.forceUpdate();
    }

    componentWillUnmount(): void {
        this.customer = defaultClient;
        this.cityComboBoxOptions = [];
        this.allStreetComboBoxOptions = [];
        this.streetComboBoxOptions = [];
        this.prevGetCitiesResponse = [];

        if(this.props && this.props.store) {
            this.props.store.clearModel();
        }

        this.forceUpdate();
    }

    onCountryChange(countryId: any) {
        this.customer.countryId = countryId;

        if(countryId !== this.idOfPoland) {
            this.cityComboBoxOptions = [];
            this.allStreetComboBoxOptions = [];
            this.streetComboBoxOptions = [];
        }

        this.forceUpdate();
    }

    private async refreshCustomerData() {
        await clientService.get({ id: this.customer.id } as ClientDto).then((response: any) => {
            if(response && response.id === this.customer.id) {
                this.customer = response;
            }
        }).catch((error: any) => {
            console.error(error);
        });

        await clientAttachedFilesService.checkIsClientHaveAgreements(this.customer.id).then((response: any) => {
            if(response && typeof response === 'object') {
                this.clientHaveAgreements = response;
            }
        }).catch((error: any) => {
            console.error(error);
        });

        await clientService.getAttachedFiles(this.customer.id).then((response: any) => {
            if(response) {
                this.clientAttachedFiles = response;
            }
        }).catch((error: any) => {
            console.error(error);
        });
        
        this.forceUpdate();
    }

    private async zipPostalCodeValueChange(value: any, preSelectedCity?: string, preSelectedStreetAddress?: string) {
        if(!!value && value.length === 6 && this.customer.countryId === this.idOfPoland) {
            this.customer.city = !!preSelectedCity ? preSelectedCity : defaultClient.city;
            this.customer.streetAddress = !!preSelectedStreetAddress ? preSelectedStreetAddress : defaultClient.streetAddress;
            this.cityComboBoxOptions = [];
            this.allStreetComboBoxOptions = [];
            this.streetComboBoxOptions = [];

            this.asyncActionInProgress = true;
            this.forceUpdate();

            await gusService.getCityInfoByPostCode(value, this.customer.nationality).then((response: any) => {
                if(response.success && response.result.length > 0) {
                    this.prevGetCitiesResponse = response.result;

                    this.customer.county = modifyFirstLetter(response.result[0].county.toLowerCase(), 'capitalizeEachWord');
                    this.customer.stateProvinceId = modifyFirstLetter(response.result[0].voivodenship.toLowerCase(), 'toLowerCase');

                    response.result.forEach((result: any) => {
                        let cityName = result.city.toLowerCase();
                        cityName = modifyFirstLetter(cityName, 'capitalizeEachWord', true);
                        this.cityComboBoxOptions.push({ key: `${cityName}`, text: cityName });
                    });

                    if(!!preSelectedCity) {
                        this.comboBoxInputValueChange(preSelectedCity, response.result);
                    }
                } else {
                    console.error(Array.isArray(response.error) ? response.error[0] : response.error);
                }

                this.asyncActionInProgress = false;
                this.forceUpdate();
            }).catch((error: any) => {
                console.error(error);
                this.asyncActionInProgress = false;
                this.forceUpdate();
            })
        } else {
            this.customer.city = defaultClient.city;
            this.customer.streetAddress = defaultClient.streetAddress;
            this.cityComboBoxOptions = [];
            this.allStreetComboBoxOptions = [];
            this.streetComboBoxOptions = [];
        }
    }

    private async comboBoxInputValueChange(value: any, preSettedCitiesResponse?: any[]) {
        this.allStreetComboBoxOptions = [];
        this.streetComboBoxOptions = [];
        
        if(!preSettedCitiesResponse) {
            this.customer.streetAddress = defaultClient.streetAddress;
        }
        
        if(!!value && value.length >= 3 && this.customer.countryId === this.idOfPoland && this.customer.zipPostalCode.length < 6) {
            this.cityComboBoxOptions = [];

            this.asyncActionInProgress = true;
            this.forceUpdate();

            await gusService.getCities(value).then((response: any) => {
                if(response.success && response.result.length > 0) {
                    this.prevGetCitiesResponse = response.result;
                    response.result.forEach((result: any) => {
                        this.cityComboBoxOptions.push({ key: `${result.city};;${result.citySymbol}`, text: result.inline });
                    });
                } else {
                    console.error(Array.isArray(response.error) ? response.error[0] : response.error);
                }

                this.asyncActionInProgress = false;
                this.forceUpdate();
            }).catch((error: any) => {
                console.error(error);
                this.asyncActionInProgress = false;
                this.forceUpdate();
            })
        } else if(value && (this.prevGetCitiesResponse.length > 0 || (preSettedCitiesResponse && preSettedCitiesResponse.length > 0))) {
            const citiesResponse: any[] = this.prevGetCitiesResponse.length > 0 ? this.prevGetCitiesResponse : (preSettedCitiesResponse ? preSettedCitiesResponse : []);

            citiesResponse.some((result: any) => {
                if(result.city === value.toUpperCase() && result.streets.length >= 1 && result.streets[0].length > 0) {
                    result.streets.sort();

                    result.streets.forEach((street: any) => {
                        let streetName = street.toLowerCase();
                        streetName = modifyFirstLetter(streetName, 'capitalizeEachWord', true);
                        
                        this.allStreetComboBoxOptions.push({ key: `${streetName}`, text: streetName });
                    });

                    return true;
                }
                return false;
            });
        }
    }

    private getOtherAddressDataByCitySymbol(citySymbol: string) {
        let foundCity: any = filterBySome(this.prevGetCitiesResponse, 'citySymbol', citySymbol);

        if(foundCity && !!foundCity.county && !!foundCity.voivodenship) {
            this.customer.county = foundCity.county.toLowerCase();
            this.customer.stateProvinceId = foundCity.voivodenship.toLowerCase();
        }

        this.forceUpdate();
    }
    
    private async getCompanyInfoBy(type: string) {
        if(this.customer[type] && !!this.customer[type] && this.customer[type].length > 0) {
            this.searchByNipOrRegonInProgress = type;
            this.forceUpdate();

            await gusService[`getCompanyInfoBy${modifyFirstLetter(type, 'toUpperCase')}`](this.customer[type]).then((response: any) => {
                if(response.success && Object.keys(response.result).length > 0) {
                    if(!!response.result.city) {
                        this.cityComboBoxOptions = [];
                        this.allStreetComboBoxOptions = [];
                        this.streetComboBoxOptions = [];
                        this.customer.city = modifyFirstLetter(response.result.city, 'capitalizeEachWord', true);
                    }
                    this.customer.county = !!response.result.county ? response.result.county : this.customer.county;
                    this.customer.company = !!response.result.name ? response.result.name : this.customer.company;
                    this.customer.streetAddress = !!response.result.street ? modifyFirstLetter(response.result.street, 'capitalizeEachWord', true) : this.customer.streetAddress;
                    this.customer.streetAddress2 = !!response.result.buildingNumber 
                            ? `${response.result.buildingNumber}${!!response.result.apartmentNumber ? ' / ' + response.result.apartmentNumber : ''}` 
                            : this.customer.streetAddress2;
                    this.customer.zipPostalCode = !!response.result.postCode ? response.result.postCode : this.customer.zipPostalCode;
                    this.customer.nip = !!response.result.nip ? response.result.nip : this.customer.nip;
                    this.customer.regon = !!response.result.regon ? response.result.regon : this.customer.regon;
                    this.customer.pkDs = !!response.result.pkDs ? response.result.pkDs : this.customer.pkDs;
                    this.customer.stateProvinceId = !!response.result.voivodenship ? response.result.voivodenship : this.customer.stateProvinceId;
                    this.customer.countryId = !!this.idOfPoland ? this.idOfPoland : this.customer.countryId;

                    if(!!response.result.postCode) {
                        this.zipPostalCodeValueChange(response.result.postCode, this.customer.city, this.customer.streetAddress);
                    }
                } else {
                    console.error(Array.isArray(response.error) ? response.error[0] : response.error);
                }
                
                this.searchByNipOrRegonInProgress = '';
                this.forceUpdate();
            }).catch((error: any) => {
                console.error(error);
                this.searchByNipOrRegonInProgress = '';
                this.forceUpdate();
            })
        }
    }

    private validateData(customer: ClientDto): any {
        const inputErrors: any = {
            pesel: '',
            nip: '',
            regon: '',
            phone: '',
            name: '',
            surname: '',
            errorsCount: 0,
            email: '',
            emailAdditional: '',
        };
        
        if(!!customer.city) {
            let splittedCustomerCity = customer.city.split(';;');
            this.customer.city = splittedCustomerCity[0];

            if(splittedCustomerCity.length > 1 && !!splittedCustomerCity[1]) {
                this.getOtherAddressDataByCitySymbol(splittedCustomerCity[1]);
            }
        }

        if((customer.clientType === ClientTypeEnum.Individual || customer.clientType === ClientTypeEnum.SoleTrader) && !!customer.pesel && customer.pesel.length > 0) {
            if(customer.pesel.match(/^[0-9]+$/) !== null) {
                if(validatePesel(customer.pesel) === false) {
                    inputErrors.pesel = L("Pesel is not valid.");
                    inputErrors.errorsCount++;
                }
            } else {
                inputErrors.pesel = L("Pesel number must consist of only 11 digits.");
                inputErrors.errorsCount++;
            }
        }

        if((customer.clientType === ClientTypeEnum.Individual || customer.clientType === ClientTypeEnum.SoleTrader) && !!customer.user.name) {
            const regEx = /^[a-zA-ZąćęłńóśźżĄĆĘŁŃÓŚŹŻ]{2,}$/;
            if(customer.user.name.length > 0 && customer.user.name.length < 2) {
                inputErrors.name = L("Name must be at least 2 digits.");
                inputErrors.errorsCount++;
            } else if(!regEx.test(customer.user.name)) {
                inputErrors.name = L("Name is not valid.");
                inputErrors.errorsCount++;
            }
        }

        if((customer.clientType === ClientTypeEnum.Individual || customer.clientType === ClientTypeEnum.SoleTrader) && !!customer.user.surname) {
            const regEx = /^([a-zA-ZąćęłńóśźżĄĆĘŁŃÓŚŹŻ]){2,}((-| )?([a-zA-ZąćęłńóśźżĄĆĘŁŃÓŚŹŻ]){2,})?( ((v|V)(e|E)(l|L)) ?([a-zA-ZąćęłńóśźżĄĆĘŁŃÓŚŹŻ]){2,})?$/;
            if(customer.user.surname.length > 0 && customer.user.surname.length < 2) {
                inputErrors.surname = L("Last name must be at least 2 digits.");
                inputErrors.errorsCount++;
            } else if(!regEx.test(customer.user.surname)) {
                inputErrors.surname = L("Last name is not valid.");
                inputErrors.errorsCount++;
            }
        }

        if(customer.clientType !== ClientTypeEnum.Individual && !!customer.nip && customer.nip.length > 0) {
            if(customer.nip.trim().match(/^[0-9]+$/) !== null) {
                if(validateNip(customer.nip.trim()) === false) {
                    inputErrors.nip = L("NIP is not valid.");
                    inputErrors.errorsCount++;
                }
            } else {
                inputErrors.nip = L("NIP number must consist of only 10 digits.");
                inputErrors.errorsCount++;
            }
        }

        if(customer.clientType !== ClientTypeEnum.Individual && !!customer.regon && customer.regon.length > 0) {
            if(customer.regon.match(/^[0-9]+$/) !== null) {
                if(validateRegon(customer.regon) === false) {
                    inputErrors.regon = L("REGON is not valid.");
                    inputErrors.errorsCount++;
                }
            } else {
                inputErrors.regon = L("REGON number must consist of only 9 or 14 digits.");
                inputErrors.errorsCount++;
            }
        }

        if(!!customer.phone && customer.phone.length > 3 && customer.phone.length < 7) {
            inputErrors.phone = L("Phone number is not valid.");
            inputErrors.errorsCount++;
        }

        const valuesToCheck: string[] = ['countryId', 'nationality', 'city', 'county', 'stateProvinceId', 'zipPostalCode', 'streetAddress', 'streetAddress2'];
        if(this.customer.clientType === null || this.customer.clientType === ClientTypeEnum.Individual || this.customer.clientType === ClientTypeEnum.SoleTrader) {
            valuesToCheck.push('user.emailAddress', 'emailAdditional', 'user.name', 'user.surname', 'pesel', 'occupation');
        }
        if(this.customer.clientType === null || this.customer.clientType !== ClientTypeEnum.Individual) {
            valuesToCheck.push('company', 'nip', 'regon');
        }

        valuesToCheck.forEach((value: string) => {
            const splittedValue: string[] = value.split('.');

            if(value === 'emailAdditional') {
                if(!!this.customer[value] && !validateEmail(this.customer[value])) {
                    inputErrors.emailAdditional = L("Email is not valid.");
                    inputErrors.errorsCount++;
                } else {
                    return;
                }
            }

            if(value === 'occupation') {
                if(!this.customer[value] || this.customer[value].length === 0 || ClientOccupationEnum[this.customer[value]] === ClientOccupationEnum.NotSpecified) {
                    inputErrors.errorsCount++;
                } else {
                    return;
                }
            }

            if(splittedValue.length === 1 && (!this.customer[value] || this.customer[value].length === 0)) {
                inputErrors.errorsCount++;
            } else if(splittedValue.length > 1) {
                let tempCustomerDeeper: any = this.customer;
                splittedValue.forEach((value: string) => {
                    tempCustomerDeeper = tempCustomerDeeper[value];
                });

                if(!tempCustomerDeeper || tempCustomerDeeper.length === 0) {
                    inputErrors.errorsCount++;
                } else if(value === 'user.emailAddress' && !validateEmail(tempCustomerDeeper)) {
                    inputErrors.email = L("Email is not valid.");
                    inputErrors.errorsCount++;
                }
            }
        });
        
        return inputErrors;
    }

    private async getPasswordToFile(type: string, uuid: string, fileId: number) {
        this.asyncActionInProgress = true;
        this.forceUpdate();

        if(type === 'apkAttachedFiles') {
            await apkAttachedFilesService.getPassword(uuid).then((response: any) => {
                if(response && !!response) {
                    this.clientFilePassword = {[fileId]: response};
                } else {
                    this.clientFilePasswordError = L('Something went wrong. Try again later or contact with administrator.');
                }
            }).catch((error: any) => {
                console.error(error);
                this.clientFilePasswordError = catchErrorMessage(error);
            });
        } else if(type === 'policyCalculationAttachedFiles') {
            await policyCalculationAttachedFilesService.getPassword(uuid).then((response: any) => {
                if(response && !!response) {
                    this.clientFilePassword = {[fileId]: response};
                } else {
                    this.clientFilePasswordError = L('Something went wrong. Try again later or contact with administrator.');
                }
            }).catch((error: any) => {
                console.error(error);
                this.clientFilePasswordError = catchErrorMessage(error);
            });
        }

        this.asyncActionInProgress = false;
        this.forceUpdate();
    }

    private async updateClientAttachment(file: ClientAttachedFilesDto) {
        this.asyncActionInProgress = true;
        this.forceUpdate();

        await clientAttachedFilesService.update(file).then(async (response: any) => {
            if(response && !!response.id && response.id > 0) {
                this.attachmentFileAsyncActionStatus[response.id] = true;
                this.forceUpdate();
                
                setTimeout(() => {
                    this.attachmentFileAsyncActionStatus[response.id] = false;
                    this.forceUpdate();
                }, 3000);

                await clientAttachedFilesService.checkIsClientHaveAgreements(this.customer.id).then((haveAgreementsResponse: any) => {
                    if(haveAgreementsResponse && typeof haveAgreementsResponse === 'object') {
                        this.clientHaveAgreements = haveAgreementsResponse;
                    }
                }).catch((haveAgreementsError: any) => {
                    console.error(haveAgreementsError);
                });
            } else {
                console.error(response);
            }
        }).catch((error: any) => {
            console.error(error);
        });

        this.asyncActionInProgress = false;
        this.forceUpdate();
    }

    private async saveClientSuperAgent() {
        if(this.customer.clientType !== ClientTypeEnum.LeasingLessor &&  this.customer.clientType !== ClientTypeEnum.Bank) {
            this.asyncActionInProgress = true;
            this.forceUpdate();
            
            const clientStructurePayload: ClientStructureDto = {
                customerId: parseInt(this.customer.id),
                userId: parseInt(this.currentUserId),
                creatorUserId: !!this.customer.creatorUserId ? this.customer.creatorUserId : 0,
                superAgentId: !!this.customer.superAgentId ? this.customer.superAgentId : 0,
                managerId: parseInt(this.superAgentManagerUser.id),
                directorId: parseInt(this.superAgentDirectorUser.id),
            };
            
            await clientService.updateClientStructure(clientStructurePayload).then((response: any) => {
                if(response) {
                    this.savedClientStructure = JSON.stringify(clientStructurePayload);
                } else {
                    this.clientStructureError = L('Something went wrong. Try again later or contact with administrator.');
                }
            }).catch((error: any) => {
                console.error(error);
                this.clientStructureError = catchErrorMessage(error);
            });
            
            this.asyncActionInProgress = false;
            this.forceUpdate();
        }
    }

    private toggleShowFilePassword(bool: boolean) {
        this.showClientFilePassword = bool; 
        this.forceUpdate();
    }

    renderContent() {
        this.customer = this.props.payload.model ? this.props.payload.model : this.props.payload;
    
        if(this.countryOptions && this.countryOptions.dropdown && this.countryOptions.dropdown.length === 0) {
            this.countryOptions.dropdown = this.allCountries;
        }
        
        let localPkd: any[] = []
        if(typeof this.customer.pkDs === "object") {
            localPkd = !Array.isArray(this.customer.pkDs) ? Object.values(this.customer.pkDs) : this.customer.pkDs
        }
        
        const mainPkDs = localPkd.filter((item: any) => item.main === true);
        const additionalPkd = localPkd.filter((item: any) => item.main === false);
        const extractedPkD = mainPkDs.map((item: any) => {
            return  { fullName: item.code + " " + item.name };
        });
        const pkd = extractedPkD.map(item => item.fullName);
        const extractedAdditionalPkd = additionalPkd.map((item: any) => {
            return { fullName: item.code + " " + item.name};
        });
        const additionalsPkd = extractedAdditionalPkd.map(item => item.fullName).join('\n');
        
        let filteredCityComboBoxOptions = filterBySome(this.cityComboBoxOptions, 'key', this.customer.city);
        if(!filteredCityComboBoxOptions && !!this.customer.city && this.customer.city.length > 0) {
            this.cityComboBoxOptions = [...this.cityComboBoxOptions, { key: this.customer.city, text: modifyFirstLetter(this.customer.city, 'capitalizeEachWord', true), hidden: true }];
        }

        let filteredStreetComboBoxOptions = filterBySome(this.allStreetComboBoxOptions, 'key', this.customer.streetAddress);

        if(this.customer.streetAddress.length > 2) {
            this.streetComboBoxOptions = [
                ...this.allStreetComboBoxOptions.filter((value: IComboBoxOption) => value.text.includes(modifyFirstLetter(this.customer.streetAddress, 'capitalizeEachWord', true)))
            ];
        } else {
            this.streetComboBoxOptions = [...this.allStreetComboBoxOptions];
        }

        if(!filteredStreetComboBoxOptions && !!this.customer.streetAddress && this.customer.streetAddress.length > 0) {
            this.streetComboBoxOptions = [...this.streetComboBoxOptions, { key: this.customer.streetAddress, text: this.customer.streetAddress, hidden: true }];
        }

        let inputErrors: any = this.validateData(this.customer);
        if(inputErrors.errorsCount !== this.inputErrorsCount) {
            this.inputErrorsCount = inputErrors.errorsCount;
            if(this.props.onSetInputErrorsCount)
                this.props.onSetInputErrorsCount(inputErrors.errorsCount);
            this.forceUpdate();
        }

        let customerAttachedFilesList: any = {
            apkAttachedFiles: [] as any,
            clientAgreements: [] as any,
            clientAttachedFiles: [] as any,
            policyAttachedFiles: [] as any,
            policyCalculationAttachedFiles: [] as any,
        };

        let customerAttachedFilesElements: any = {
            apkAttachedFiles: [] as JSX.Element[],
            clientAgreements: [] as JSX.Element[],
            clientAttachedFiles: [] as JSX.Element[],
            policyAttachedFiles: [] as JSX.Element[],
            policyCalculationAttachedFiles: [] as JSX.Element[],
        };

        if(this.clientAttachedFiles && Object.keys(this.clientAttachedFiles).length > 0) {
            for(let key in this.clientAttachedFiles) {
                if(this.clientAttachedFiles.hasOwnProperty(key) && this.clientAttachedFiles[key].length > 0 && customerAttachedFilesList[key]) {
                    const files: any = this.clientAttachedFiles[key];
                    
                    files.forEach((file: any) => {
                        if(typeof file.isHiddenInClientInfo === 'undefined' || !file.isHiddenInClientInfo) {
                            customerAttachedFilesList[key].push(file);
                        }
                    });

                    if(customerAttachedFilesList[key]) {
                        const visibleFiles = files.filter((file: any) => file.hasOwnProperty('isHiddenInClientInfo') && file.isHiddenInClientInfo !== true);
                        customerAttachedFilesList[key].push(...visibleFiles);

                        customerAttachedFilesList[key].sort((a: any, b: any) => new Date(b.creationTime).getTime() - new Date(a.creationTime).getTime());
                        customerAttachedFilesList[key]['grouped'] = [] as any[];
                        customerAttachedFilesList[key]['single'] = [] as any[];
                        customerAttachedFilesList[key]['alreadyPushedIds'] = [] as number[];
                    }
                }
            }
        }

        for(let key in customerAttachedFilesList) {
            if(customerAttachedFilesList.hasOwnProperty(key) && customerAttachedFilesList[key].length > 0) {
                customerAttachedFilesList[key].forEach((file: any, index: number) => {
                    if(file.creationTime && customerAttachedFilesList[key][index + 1]) {
                        const file1DateTime: number = new Date(file.creationTime).getTime();
                        const file2DateTime: number = new Date(customerAttachedFilesList[key][index + 1].creationTime).getTime();

                        if(file1DateTime - file2DateTime < 12000) {
                            if(!customerAttachedFilesList[key]['alreadyPushedIds'].includes(file.id)) {
                                if(customerAttachedFilesList[key]['grouped'].length === 0) {
                                    customerAttachedFilesList[key]['grouped'][0] = [];
                                }
                                customerAttachedFilesList[key]['grouped'][customerAttachedFilesList[key]['grouped'].length - 1].push(file);
                            }
                            customerAttachedFilesList[key]['grouped'][customerAttachedFilesList[key]['grouped'].length - 1].push(customerAttachedFilesList[key][index + 1]);
                            customerAttachedFilesList[key]['alreadyPushedIds'].push(customerAttachedFilesList[key][index + 1].id);
                        } else if(!customerAttachedFilesList[key]['alreadyPushedIds'].includes(file.id)) {
                            customerAttachedFilesList[key]['single'].push(file);
                        } else {
                            customerAttachedFilesList[key]['grouped'][customerAttachedFilesList[key]['grouped'].length] = [];
                        }
                    } else if(!customerAttachedFilesList[key]['alreadyPushedIds'].includes(file.id)) {
                        customerAttachedFilesList[key]['single'].push(file);
                    }

                    if(!customerAttachedFilesList[key]['alreadyPushedIds'].includes(file.id)) {
                        customerAttachedFilesList[key]['alreadyPushedIds'].push(file.id);
                    }
                });
            }
        }

        for(let key in customerAttachedFilesList) {
            if(customerAttachedFilesList.hasOwnProperty(key) && customerAttachedFilesList[key].length > 0) {
                customerAttachedFilesList[key]['grouped'].forEach((group: any, index: number) => {
                    if(customerAttachedFilesList[key]['grouped'][0].status !== ClientAttachedFileStatus.Rejected && this.firstStartFlag) {
                        this.agreementsSentToClient = true;
                        this.firstStartFlag = false;
                    }

                    if(group.length > 0) {
                        customerAttachedFilesElements[key].push(
                            {
                                creationTime: group[0].creationTime,
                                element: <li key={`group${index}`} className={classNames.attachedFilesListGroupedWrapper}>
                                {
                                    group.map((file: any) => {
                                        return <div className={classNames.attachedFilesListGroupedItem}>
                                            <div>
                                                <div style={{color: myTheme.palette.neutralTertiaryAlt}}>
                                                    <a href={file.fileUrl} title={L("Download file")} style={{margin: '0 35px 0 50px', position: 'relative'}}>
                                                        <Icon iconName='Download' style={{margin: 0, fontSize: 20, position: 'absolute', left: '-50px', cursor: 'pointer', color: 'green'}} />
                                                        
                                                        {file.originalFileName}
                                                    </a>
                                                    
                                                    {dateFormat(file.creationTime, "DD.MM.YYYY HH:mm", true)}
                                                </div>
                                                {!!file.description && 
                                                    <div style={{marginTop: 15, marginLeft: 50}}>
                                                        {file.description}
                                                    </div>
                                                }
                                            </div>

                                            {key === 'clientAttachedFiles' &&
                                                <Stack horizontal={true}>
                                                    <DropdownBase key={'fileStatus'} required={false} label={L('Status')} options={this.fileStatusOptions.dropdown} value={this.selectedFileStatus[file.id] ? this.selectedFileStatus[file.id] : file.status}
                                                        disabled={this.asyncActionInProgress || file.status === ClientAttachedFileStatus.Sended || file.status === ClientAttachedFileStatus.Rejected || this.selectedFileStatus[file.id] === ClientAttachedFileStatus.Rejected}
                                                        customClassName={classNames.fileStatusDropdown} customLabelStyles={{marginTop: '-20px'}} isDataLoaded={true}
                                                        onChange={(e) => {
                                                            if(!!e && e !== file.status) {
                                                                // if(this.selectedFileStatus[file.id]) {
                                                                    this.updateClientAttachment({...file, status: e});
                                                                // }
                    
                                                                this.selectedFileStatus[file.id] = e;
                                                                this.forceUpdate();
                                                            }
                                                        }} />
                    
                                                    { this.attachmentFileAsyncActionStatus[file.id] === true &&
                                                        <Icon iconName='CheckMark' style={{color: 'green', cursor: 'default'}} className={classNames.inputIcon} title={L('Attachment status changed successfully.')} />
                                                    }
                                                </Stack>
                                            }
                                            
                                            {file.uuid && !!file.uuid && 
                                                <Stack horizontal={true}>
                                                    { !this.clientFilePassword[file.id] &&
                                                        <PrimaryButton 
                                                            key={`showPasswordButton${file.id}`}
                                                            className={classNames.uploadButton}
                                                            theme={myTheme}
                                                            text={L('Get file password')}
                                                            type={'button'}
                                                            onClick={() => this.getPasswordToFile(key, file.uuid, file.id)}
                                                            disabled={this.asyncActionInProgress === true || !!this.clientFilePassword[file.id]}
                                                            style={{padding: '18px', marginTop: 0}}
                                                        />
                                                    }
        
                                                    {!!this.clientFilePassword[file.id] &&
                                                        <>
                                                            <TextFieldBase type={this.showClientFilePassword === true ? `text` : `password`} label={L('')} theme={myTheme} value={this.clientFilePassword[file.id]} isDataLoaded={true} disabled={true}
                                                                className={classNames.filePasswordInput} />
        
                                                            <Icon iconName='RedEye' className={classNames.inputIcon} title={L('Hold to reveal password')} 
                                                                onMouseDown={() => this.toggleShowFilePassword(true)} onMouseUp={() => this.toggleShowFilePassword(false)}
                                                                style={{color: this.showClientFilePassword ? 'green' : 'black'}}
                                                            />
                                                        </>
                                                    }
                                                </Stack>
                                            }
                                        </div>
                                    })
                                }
                            </li>
                            }
                        );
                    }
                });
            }
        }

        for(let key in customerAttachedFilesList) {
            if(customerAttachedFilesList.hasOwnProperty(key) && customerAttachedFilesList[key].length > 0) {
                customerAttachedFilesList[key]['single'].forEach((file: any) => {
                    if(customerAttachedFilesList[key]['single'][0].status !== ClientAttachedFileStatus.Rejected && this.firstStartFlag) {
                        this.agreementsSentToClient = true;
                        this.firstStartFlag = false;
                    }
                    
                    customerAttachedFilesElements[key].push(
                        {
                            creationTime: file.creationTime,
                            element: <li key={file.id} className={classNames.attachedFilesListItem}>
                            <div>
                                <div style={{color: myTheme.palette.neutralTertiaryAlt}}>
                                    <a href={file.fileUrl} title={L("Download file")} style={{margin: '0 35px 0 50px', position: 'relative'}}>
                                        <Icon iconName='Download' style={{margin: 0, fontSize: 20, position: 'absolute', left: '-50px', cursor: 'pointer', color: 'green'}} />
                                        
                                        {file.originalFileName}
                                    </a>
                                    
                                    {dateFormat(file.creationTime, "DD.MM.YYYY HH:mm", true)}
                                </div>
                                {!!file.description && 
                                    <div style={{marginTop: 15, marginLeft: 50}}>
                                        {file.description}
                                    </div>
                                }
                            </div>

                            {key === 'clientAttachedFiles' &&
                                <Stack horizontal={true}>
                                    <DropdownBase key={'fileStatus'} required={false} label={L('Status')} options={this.fileStatusOptions.dropdown} value={this.selectedFileStatus[file.id] ? this.selectedFileStatus[file.id] : file.status}
                                        disabled={this.asyncActionInProgress || file.status === ClientAttachedFileStatus.Sended || file.status === ClientAttachedFileStatus.Rejected || this.selectedFileStatus[file.id] === ClientAttachedFileStatus.Rejected} 
                                        customClassName={classNames.fileStatusDropdown} customLabelStyles={{marginTop: '-20px'}} isDataLoaded={true}
                                        onChange={(e) => {
                                            if(!!e && e !== file.status) {
                                                // if(this.selectedFileStatus[file.id]) {
                                                    this.updateClientAttachment({...file, status: e});
                                                // }
                                                if (e !== ClientAttachedFileStatus.Rejected) {
                                                    this.agreementsSentToClient = true
                                                } else {
                                                    this.agreementsSentToClient = false
                                                }
    
                                                this.selectedFileStatus[file.id] = e;
                                                this.forceUpdate();
                                            }
                                        }} />
    
                                    { this.attachmentFileAsyncActionStatus[file.id] === true &&
                                        <Icon iconName='CheckMark' style={{color: 'green', cursor: 'default'}} className={classNames.inputIcon} title={L('Attachment status changed successfully.')} />
                                    }
                                </Stack>
                            }
                            
                            {file.uuid && !!file.uuid && 
                                <Stack horizontal={true}>
                                    <PrimaryButton 
                                        key={`showPasswordButton${file.id}`}
                                        className={classNames.uploadButton}
                                        theme={myTheme}
                                        text={L('Get file password')}
                                        type={'button'}
                                        onClick={() => this.getPasswordToFile(key, file.uuid, file.id)}
                                        disabled={this.asyncActionInProgress === true || !!this.clientFilePassword[file.id]}
                                        style={{padding: '18px', marginTop: 0}}
                                    />

                                    {!!this.clientFilePassword[file.id] &&
                                        <>
                                            <TextFieldBase type={this.showClientFilePassword === true ? `text` : `password`} label={L('')} theme={myTheme} value={this.clientFilePassword[file.id]} isDataLoaded={true} disabled={true}
                                                className={classNames.filePasswordInput} />

                                            <Icon iconName='RedEye' className={classNames.inputIcon} title={L('Hold to reveal password')} 
                                                onMouseDown={() => this.toggleShowFilePassword(true)} onMouseUp={() => this.toggleShowFilePassword(false)}
                                                style={{color: this.showClientFilePassword ? 'green' : 'black'}}
                                            />
                                        </>
                                    }
                                </Stack>
                            }
                        </li>
                        }
                    );
                });
            }
        }
        const pivotStyles = {
            root: {
                marginLeft: '-8px'
            },
            linkIsSelected: {
                color: myTheme.palette.red,
                selectors: {
                    ':before': {
                        height: '5px',
                        backgroundColor: additionalTheme.darkerRed,
                    }
                }
            }
        };

        customerAttachedFilesElements.apkAttachedFiles.sort((a: any, b: any) => new Date(b.creationTime).getTime() - new Date(a.creationTime).getTime());

        return <Pivot theme={myTheme} styles={pivotStyles}>
            <PivotItem headerText={L('General')} key={'General'}>
                <ChoiceGroupBase label={L("Customer type")} value={this.customer.clientType} required={true}
                    disabled={!this.isDataLoaded} options={enumToChoiceGroupOptions(ClientTypeEnum, true, true, "string")}
                    onChange={(e: any) => {
                        this.customer.clientType = e.key;
                        this.forceUpdate();
                    }}
                />

                {(this.customer.clientType === null || this.customer.clientType === ClientTypeEnum.Individual) && <>
                    { this.renderElement(new ContentViewModelProperty('user.name', L('First name'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, useModifyFirstLetter: 'capitalizeEachWord', useRemoveNumbersFromString: true, useTrim: true}),
                        {'user.name': inputErrors.name}, {'user.name': this.customer.user.name}) 
                    }
                    { this.renderElement(new ContentViewModelProperty('user.surname', L('Last name'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, useModifyFirstLetter: 'capitalizeEachWord', useRemoveNumbersFromString: true, useTrim: true, validationData: {minlength: 2}}), 
                        {'user.surname': inputErrors.surname}, {'user.surname': this.customer.user.surname}) 
                    }
                </>}

                {(this.customer.clientType === null || this.customer.clientType === ClientTypeEnum.Individual) && 
                    this.renderElement(new ContentViewModelProperty('pesel', L('Pesel'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, validationData: {
                        mask: "***********", maskFormat: {'*': /[0-9]/}, maskChar: ""
                    }, useTrim: true}), {'pesel': inputErrors.pesel}, {'pesel': this.customer.pesel})
                }

                {(this.customer.clientType === null || this.customer.clientType === ClientTypeEnum.Individual) && 
                    <>
                        {this.renderElement(new ContentViewModelProperty('user.emailAddress', L('Email'), Controls.Text, true, [], !!this.customer.id ? true : false, {isDataLoaded: this.isDataLoaded, textType: 'email', useModifyLettersCase: 'toLowerCase'}), {'user.emailAddress': inputErrors.email}, {'user.emailAddress': this.customer.user.emailAddress})}
                        <CheckBoxBase label={L("Has another correspondence email address")} 
                                value={this.showAdditionalEmailInput} disabled={!this.isDataLoaded}
                                onChange={(e: any) => {
                                    this.showAdditionalEmailInput = !this.showAdditionalEmailInput;
                                    this.forceUpdate();
                                }}
                        />
                        {this.showAdditionalEmailInput &&
                            this.renderElement(new ContentViewModelProperty('emailAdditional', L('Correspondence email address'), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, textType: 'email', useModifyLettersCase: 'toLowerCase'}), {'emailAdditional': inputErrors.emailAdditional}, {'emailAdditional': this.customer.emailAdditional})
                        }    
                    </>
                }

                {(this.customer.clientType === null || this.customer.clientType === ClientTypeEnum.Individual) && 
                    <>
                        {this.renderElement(new ContentViewModelProperty('phone', L('Phone'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, useTrim: true, validationData: {
                            mask: "+** *** *** ***", maskFormat: {'*': /[0-9]/}, maskChar: "", maxlength: 15,
                        }}), {'phone': inputErrors.phone}, {'phone': this.customer.phone})} 
                    </>
                }

                {(this.customer.clientType === null || this.customer.clientType !== ClientTypeEnum.Individual) && 
                    <Stack horizontal={true}>
                        {this.renderElement(new ContentViewModelProperty('nip', L('NIP'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, useTrim: true, validationData: {
                            mask: "**********", maskFormat: {'*': /[0-9]/}, maskChar: ""
                        }}), {'nip': inputErrors.nip}, {'nip': this.customer.nip})}
                        <Icon iconName='DownloadDocument' className={classNames.inputIcon} onClick={() => this.getCompanyInfoBy('nip') } title={L('Get company info by NIP')}
                            style={{marginTop: '26px'}} />
                        
                        {this.searchByNipOrRegonInProgress === 'nip' &&
                            <Spinner label={L("Getting company info")} className={classNames.smallLoadSpinner} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" />
                        }
                    </Stack>
                }

                {(this.customer.clientType === null || this.customer.clientType !== ClientTypeEnum.Individual) && 
                    <Stack horizontal={true}>
                        {this.renderElement(new ContentViewModelProperty('regon', L('REGON'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, useTrim: true, validationData: {
                            mask: "**************", maskFormat: {'*': /[0-9]/}, maskChar: ""
                        }}), {'regon': inputErrors.regon}, {'regon': this.customer.regon})}
                        <Icon iconName='DownloadDocument' className={classNames.inputIcon} onClick={() => this.getCompanyInfoBy('regon') } title={L('Get company info by REGON')}
                            style={{marginTop: '26px'}} />

                        {this.searchByNipOrRegonInProgress === 'regon' &&
                            <Spinner label={L("Getting company info")} className={classNames.smallLoadSpinner} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" />
                        }
                    </Stack>
                }
                {(this.customer.clientType === ClientTypeEnum.SoleTrader || this.customer.clientType === ClientTypeEnum.Company) &&
                    <>
                        <Stack horizontal={true}>
                            {this.renderElement(new ContentViewModelProperty('pkd', L('PKD'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, rows: 2}), {'pkd': inputErrors.pkd}, {'pkd': pkd})}
                        </Stack>

                        <CheckBoxBase label={L("Has other PKD")}
                            value={this.showAdditionalPkdInput} disabled={!this.isDataLoaded}
                            onChange={(e: any) => {
                                this.showAdditionalPkdInput = !this.showAdditionalPkdInput;
                                this.forceUpdate();
                            }}
                        />

                        {this.showAdditionalPkdInput && 
                            this.renderElement(new ContentViewModelProperty('mainPKD', L('Other main PKD'), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, useTrim: true}), [], {'mainPKD': this.customer.mainPKD})
                        } 

                        <Stack horizontal={true}>
                            {this.renderElement(new ContentViewModelProperty('additionalPkd', L('Additional PKD'), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, rows: 7}), {'additionalPkd': inputErrors.additionalPkd}, {'additionalPkd': additionalsPkd})}
                        </Stack>
                    </>
                }

                {(this.customer.clientType === null || this.customer.clientType !== ClientTypeEnum.Individual) && 
                    <>
                        {this.renderElement(new ContentViewModelProperty('company', L('Company name'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded}), [], {'company': this.customer.company})}
                    </>
                }

                {(this.customer.clientType === null || this.customer.clientType !== ClientTypeEnum.Individual) && 
                    <>
                        {this.renderElement(new ContentViewModelProperty('user.emailAddress', L('Email'), Controls.Text, true, [], !!this.customer.id ? true : false, {isDataLoaded: this.isDataLoaded, textType: 'email', useModifyLettersCase: 'toLowerCase'}), {'user.emailAddress': inputErrors.email}, {'user.emailAddress': this.customer.user.emailAddress})}
                        
                        <CheckBoxBase label={L("Has another correspondence email address")} 
                            value={this.showAdditionalEmailInput} disabled={!this.isDataLoaded}
                            onChange={(e: any) => {
                                this.showAdditionalEmailInput = !this.showAdditionalEmailInput;
                                this.forceUpdate();
                            }}
                        />

                        {this.showAdditionalEmailInput &&
                            this.renderElement(new ContentViewModelProperty('emailAdditional', L('Correspondence email address'), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, textType: 'email', useModifyLettersCase: 'toLowerCase'}), {'emailAdditional': inputErrors.emailAdditional}, {'emailAdditional': this.customer.emailAdditional})
                        }    
                    </>
                }

                {(this.customer.clientType === null || this.customer.clientType !== ClientTypeEnum.Individual) && 
                    <>
                        {this.renderElement(new ContentViewModelProperty('phone', L('Phone'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, validationData: {
                            mask: "+** *** *** ***", maskFormat: {'*': /[0-9]/}, maskChar: "", maxlength: 15,
                        }}), {'phone': inputErrors.phone}, {'phone': this.customer.phone})} 
                    </>
                }

                {(this.customer.clientType === null || this.customer.clientType === ClientTypeEnum.SoleTrader) && <>
                    { this.renderElement(new ContentViewModelProperty('user.name', L('First name'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, useModifyFirstLetter: 'capitalizeEachWord', useRemoveNumbersFromString: true, useTrim: true}),
                        {'user.name': inputErrors.name}, {'user.name': this.customer.user.name}) 
                    }
                    { this.renderElement(new ContentViewModelProperty('user.surname', L('Last name'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, useModifyFirstLetter: 'capitalizeEachWord', useRemoveNumbersFromString: true, useTrim: true, validationData: {minlength: 2}}),
                        {'user.surname': inputErrors.surname}, {'user.surname': this.customer.user.surname})
                    }
                </>
                }

                {(this.customer.clientType === null || this.customer.clientType === ClientTypeEnum.SoleTrader) && 
                    this.renderElement(new ContentViewModelProperty('pesel', L('Pesel'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, useTrim: true, validationData: {
                        mask: "***********", maskFormat: {'*': /[0-9]/}, maskChar: ""
                    }}), {'pesel': inputErrors.pesel}, {'pesel': this.customer.pesel})
                }
    
                {(this.customer.clientType === null || this.customer.clientType === ClientTypeEnum.Individual || this.customer.clientType === ClientTypeEnum.SoleTrader) &&                 
                    <NationalityComboBox key='nationality' label={L('Nationality')} required={true} value={this.customer.nationality} allowFreeform={true}
                        countryStore={this.props.countryStore} 
                        onInputChange={(id: string | number | undefined, value: any) => { 
                            if(!!id && id !== this.customer.nationality) {
                                id = id && typeof id === 'string' ? id.trim() : id;
                                this.customer.nationality = id?.toString();
                                this.forceUpdate();
                        } }}
                    />
                }

                <CountriesComboBox key='countryId' label={L('Country')} required={true} value={this.customer.countryId} allowFreeform={true}
                    countryStore={this.props.countryStore} getCountryId={(value: string) => { this.idOfPoland = value; this.forceUpdate(); }}
                    onInputChange={(id: string | number | undefined, value: any) => { 
                        if(!!id && id !== this.customer.countryId) {
                            id = id && typeof id === 'string' ? id.trim() : id;
                            this.onCountryChange(id); 
                    } }}
                />

                <Stack horizontal={true}>
                    {this.renderElement(new ContentViewModelProperty('zipPostalCode', L('Zip postal code'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, useTrim: true, validationData: {
                        mask: "**-***", maskFormat: {'*': /[0-9]/}, maskChar: "", maxlength: 6}, additionalMethod: (value: string) => { this.zipPostalCodeValueChange(value); }
                    }), [], {'zipPostalCode': this.customer.zipPostalCode})}

                    {this.asyncActionInProgress &&
                        <Spinner label={L("Getting data")} className={classNames.smallLoadSpinner} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" />
                    }
                </Stack>

                <Stack horizontal={true}>
                    {this.renderElement(new ContentViewModelProperty('city', L('City'), Controls.ComboBox, true, this.cityComboBoxOptions, false, {
                        isDataLoaded: this.isDataLoaded, openListOnFocus: true, useModifyFirstLetter: 'capitalizeEachWord', useRemoveNumbersFromString: true, useTrim: true,
                        additionalMethod: (value: string) => { this.comboBoxInputValueChange(value); }
                    }), [], {'city': this.customer.city})}
                    {this.asyncActionInProgress &&
                        <Spinner label={L("Getting cities")} className={classNames.smallLoadSpinner} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" />
                    }
                </Stack>

                {this.renderElement(new ContentViewModelProperty('county', L('County'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, useModifyFirstLetter: 'capitalizeEachWord', useRemoveNumbersFromString: true, useTrim: true}), [], {'county': this.customer.county})}
                {this.renderElement(new ContentViewModelProperty('streetAddress', L('Street'), Controls.ComboBox, true, this.streetComboBoxOptions, false, {
                    isDataLoaded: this.isDataLoaded, openListOnFocus: true, useModifyFirstLetter: 'capitalizeEachWord', useTrim: true,
                    additionalMethod: (value: string) => { this.customer.streetAddress = value; this.forceUpdate(); } 
                }), [], {'streetAddress': this.customer.streetAddress})}
                {this.renderElement(new ContentViewModelProperty('stateProvinceId', L('State province'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, useRemoveNumbersFromString: true, useTrim: true}), [], {'stateProvinceId': this.customer.stateProvinceId ? this.customer.stateProvinceId.toLowerCase() : ''})}
                {this.renderElement(new ContentViewModelProperty('streetAddress2', L('Home number / apartment number'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded}), [], {'streetAddress2': this.customer.streetAddress2})}
                
                {(this.customer.clientType === ClientTypeEnum.Individual || this.customer.clientType === ClientTypeEnum.SoleTrader) && 
                    <>
                        {this.renderElement(new ContentViewModelProperty('occupation', L('Occupation'), Controls.ComboBox, true, enumToDropdownOptions(ClientOccupationEnum, false, false, "string", true), false, {
                            isDataLoaded: this.isDataLoaded, openListOnFocus: true, useTrim: true,
                            additionalMethod: (value: string) => { this.customer.occupation = value; this.forceUpdate(); } 
                        }), [], {'occupation': this.customer.occupation})}

                        <ChoiceGroupBase label={L("Marital status")} value={!!this.customer.maritalStatus ? this.customer.maritalStatus : defaultClient.maritalStatus} required={true}
                            disabled={!this.isDataLoaded} options={enumToChoiceGroupOptions(ClientMaritalStatusEnum, true, true, "string")}
                            onChange={(e: any) => {
                                this.customer.maritalStatus = e.key;
                                this.forceUpdate();
                            }}
                        />
                    </>
                }

                {this.renderElement(new ContentViewModelProperty('note', L('Note'), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, rows: 5}), [], {'note': this.customer.note})}
                
                {(this.customer.clientType !== ClientTypeEnum.LeasingLessor &&  this.customer.clientType !== ClientTypeEnum.Bank) &&
                    <>
                        {this.renderElement(new ContentViewModelProperty('attendant', L('Attendant'), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'attendant': !!this.customer.creatorUserId ? this.creatorUserFullName : (this.currentUser ? this.currentUser.fullName : '')})}
                        
                        {(!!this.customer.superAgentId || (isGranted('Client.UpdateClientStructure.A') || isGranted('Client.UpdateClientStructure.O') || isGranted('Client.UpdateClientStructure.S'))) && 
                            <>
                                { (isGranted('Client.UpdateClientStructure.A') || isGranted('Client.UpdateClientStructure.O') || isGranted('Client.UpdateClientStructure.S')) ? 
                                    <>
                                        <div className={fluentTableClassNames.contentContainer}>
                                            <UserFluentListBase
                                                // searchText={this.selectClientSearchText}
                                                searchText={undefined}
                                                items={this.props.userCrudStore?.dataSet && this.props.userCrudStore?.dataSet.items ? this.props.userCrudStore?.dataSet.items : []}
                                                store={this.props.userCrudStore!}
                                                history={this.props.history}
                                                scrollablePanelMarginTop={70}
                                                customData={{
                                                    customLabel: L('SuperAgent'),
                                                    selectedUser: !!this.selectedSuperAgentFullName ? `${this.selectedSuperAgentFullName}` : undefined,
                                                }}
                                                // customOnSearchTextChanged={(text: string) => { this.selectClientSearchText = text; this.forceUpdate(); }}
                                                customSelection={this._userListSuperAgentSelection}
                                                customOnSelectionChanged={(selection: any) => {
                                                    if(typeof selection === 'string' && selection === 'deleteUser') {
                                                        this.superAgentDirectorUser = {...defaultUser};
                                                        this.superAgentManagerUser = {...defaultUser};
                                                        this.customer.superAgentId = null;
                                                        this.selectedSuperAgentFullName = "";
                                                        this.forceUpdate();
                                                    }
                                                }}
                                            />
                                        </div>

                                        {/* <PrimaryButton className={classNames.uploadButton} theme={myTheme} text={L('Save client structure')} type={'button'}
                                            onClick={() => this.saveClientSuperAgent()} disabled={this.asyncActionInProgress === true}
                                            iconProps={{ iconName: !!this.savedClientStructure ? 'CheckMark' : (!!this.clientStructureError ? 'Error' : 'Save') }}
                                        /> */}

                                        {!!this.clientStructureError &&
                                            <MessageBar messageBarType={MessageBarType.error} isMultiline={false} className={`${classNames.messageBar}`}
                                                onDismiss={() => { this.clientStructureError = ''; this.forceUpdate(); }} style={{marginLeft: 0}}
                                            >
                                                {this.clientStructureError}
                                            </MessageBar>
                                        }
                                    </>
                                    :
                                    this.renderElement(new ContentViewModelProperty('superAgentFullName', L('SuperAgent'), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded && !this.asyncActionInProgress}), [], {'superAgentFullName': this.selectedSuperAgentFullName})
                                }
                                
                                {this.renderElement(new ContentViewModelProperty('superAgentManager', L('Manager'), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded && !this.asyncActionInProgress}), [], {'superAgentManager': this.superAgentManagerUser.fullName})}
                                {this.renderElement(new ContentViewModelProperty('superAgentDirector', L('Director'), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded && !this.asyncActionInProgress}), [], {'superAgentDirector': this.superAgentDirectorUser.fullName})}
                            </>
                        }
                    </>
                }
            </PivotItem>

            { (this.customer && !!this.customer.id && (typeof this.customer.id === 'number' && this.customer.id > 0)) &&
                <PivotItem headerText={L('Consents')} key={'Consents'}>
                    <CustomerConsentsTab
                        isDataLoaded={this.isDataLoaded}
                        asyncActionInProgress={this.asyncActionInProgress}
                        customer={this.customer}
                        selectedConsentCheckboxes={this.selectedConsentCheckboxes}
                        clientHaveAgreements={this.clientHaveAgreements}
                        agreementsSentToClient={this.agreementsSentToClient}
                        agreementsSentToClientError={this.agreementsSentToClientError}
                        agreementsSentToClientMessageBarType={this.agreementsSentToClientMessageBarType}
                        clientAttachedFiles={customerAttachedFilesElements.clientAgreements}
                        mobileAgreements={this.mobileAgreements}
                        onMessageBarDismiss={() => { this.agreementsSentToClientError = ''; this.forceUpdate(); }}
                        setAsyncActionInProgress={(value: boolean) => { this.asyncActionInProgress = value; this.forceUpdate(); }}
                        setAgreementsSentToClient={(value: boolean) => { this.agreementsSentToClient = value; this.forceUpdate(); }}
                        setAgreementsSentToClientError={(value: string, messageBarType?: MessageBarType) => { 
                            this.agreementsSentToClientError = value;
                            this.agreementsSentToClientMessageBarType = !!messageBarType ? messageBarType : MessageBarType.error;
                            this.forceUpdate();
                        }}
                        setSelectedConsentCheckboxes={(selectedConsentCheckboxes: SelectedConsentCheckboxes) => { this.selectedConsentCheckboxes = selectedConsentCheckboxes; this.forceUpdate(); }}
                        setClientAttachedFiles={(value: any) => { this.clientAttachedFiles = value; this.forceUpdate(); }}
                        setCustomerToPayloadModel={() => { this.customer = this.props.payload.model ? this.props.payload.model : this.props.payload; this.forceUpdate(); }}
                        refreshCustomerData={() => this.refreshCustomerData() }
                    />
                </PivotItem>
            }

            { (this.customer && !!this.customer.id && (typeof this.customer.id === 'number' && this.customer.id > 0)) &&
                <PivotItem headerText={L('APK')} key={'ApkFiles'}>
                    <CustomerApkTab asyncActionInProgress={this.asyncActionInProgress} clientFilePasswordError={this.clientFilePasswordError}
                        apkAttachedFiles={customerAttachedFilesElements.apkAttachedFiles}
                        onMessageBarDismiss={() => { this.clientFilePasswordError = ''; this.forceUpdate(); }}
                    />
                </PivotItem>
            }

            { (this.customer && !!this.customer.id && (typeof this.customer.id === 'number' && this.customer.id > 0)) &&
                <PivotItem headerText={L('Policies')} key={'policies'}>
                    <CustomerPoliciesTab asyncActionInProgress={this.asyncActionInProgress} clientFilePasswordError={this.clientFilePasswordError}
                        policyAttachedFiles={customerAttachedFilesElements.policyAttachedFiles}
                        onMessageBarDismiss={() => { this.clientFilePasswordError = ''; this.forceUpdate(); }}
                    />
                </PivotItem>
            }

            { (this.customer && !!this.customer.id && (typeof this.customer.id === 'number' && this.customer.id > 0)) &&
                <PivotItem headerText={L('Calculations')} key={'PolicyFiles'}>
                    <CustomerCalculationsTab asyncActionInProgress={this.asyncActionInProgress} clientFilePasswordError={this.clientFilePasswordError}
                        policyCalculationAttachedFiles={customerAttachedFilesElements.policyCalculationAttachedFiles}
                        onMessageBarDismiss={() => { this.clientFilePasswordError = ''; this.forceUpdate(); }}
                    />
                </PivotItem>
            }

            { (this.customer && !!this.customer.id && (typeof this.customer.id === 'number' && this.customer.id > 0)) &&
                <PivotItem headerText={L('Vehicle list')} key={'vehicleList'}>
                    <CustomerVehicleListTab />
                </PivotItem>
            }

            { (this.customer && !!this.customer.id && (typeof this.customer.id === 'number' && this.customer.id > 0)) &&
                <PivotItem headerText={L('Claim list')} key={'claimList'}>
                    <CustomerClaimListTab />
                </PivotItem>
            }

            {this.customer && !!this.customer.id && typeof this.customer.id === "number" && this.customer.id > 0 && 
                <PivotItem headerText={L("Client contests")} key={"clientContests"}>
                    <CustomerContestTab customerId={this.customer.id} asyncActionInProgress={this.asyncActionInProgress} />
                </PivotItem>
            }
        </Pivot>
    }
}