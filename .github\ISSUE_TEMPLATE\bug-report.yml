---
name: Bug report
description: <PERSON><PERSON><PERSON><PERSON><PERSON> błąd i napisz o problemie.
title: "[bug] Krótki tytuł błędu"
labels: ["bug"]
assignees:
  - croppek
body:
  - type: textarea
    id: problem
    attributes:
      label: 🐛 Problem do rozwiązania
      description: <PERSON><PERSON><PERSON> błąd.
      placeholder: Opisz krok po kroku co nie działa.
    validations:
      required: true

  - type: dropdown
    id: version
    attributes: 
      label: 🕹️ Środowisko
      description: <PERSON><PERSON><PERSON><PERSON> środowisko, w którym występuje błąd
      options:
        - dev
        - uat
        - pre-prod
        - prod
      multiple: true
    validations:
      required: true

  - type: dropdown
    id: mark
    attributes: 
      label: 🔍 Miejsce wystąpienia
      description: Wybierz segment, którego dotyczy błąd.
      options:
        - klient
        - pojazd
        - kalkulacja
        - oferta
        - wniosek
        - polisa
        - szkoda
        - apk
        - inne
      multiple: true
    validations:
      required: true

  - type: input
    id: id-calculation
    attributes:
      label: 🎯 ID kalkulacji
      placeholder: <PERSON><PERSON><PERSON><PERSON> tutaj wpisać ID kalkulacji.
    validations:
      required: false

  - type: textarea
    id: screenshot
    attributes:
      label: 📸 Aktualny widok
      description: Załącz zrzuty ekranu pokazujące problem.
      placeholder: Mo<PERSON>esz tu wkleić obrazy lub linki.
    validations:
      required: false

  - type: textarea
    id: expected-behavior
    attributes:
      label: 🛠️ Oczekiwane zachowanie
      description: Jak funkcjonalność powinna działać?
      placeholder: Opisz, co powinno się wydarzyć zamiast obecnego zachowania.
    validations:
      required: true

  - type: textarea
    id: additional-info
    attributes:
      label: 🔗 Dodatkowe informacje
      description: Linki do makiet, powiązanych zgłoszeń, notatek.
      placeholder: Wklej tutaj linki lub inne przydatne informacje.
    validations:
      required: false
