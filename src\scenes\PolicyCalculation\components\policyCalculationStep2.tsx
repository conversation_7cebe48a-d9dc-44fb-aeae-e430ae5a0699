import { FocusZone, FocusZoneDirection, FocusZoneTabbableElements, FontWeights, IButtonStyles, IChoiceGroupOption, IconButton, IDropdownOption, IIconProps, mergeStyleSets, Modal, Pivot, PivotItem, Selection, SelectionMode, addYears } from "@fluentui/react";
import React from "react";
import { L } from "../../../lib/abpUtility";
import AppConfig from "../../../lib/appconfig";
import AppConsts from "../../../lib/appconst";
import {additionalTheme, myTheme} from "../../../styles/theme";
import { getDropdownOptionsFromDataSource, getInputIconData, getInputTableData, getInputValidationData, isInputInTab, isInputFastCalculation, shouldSaveAsTemplateForCalculationAdjust, shouldSaveAsTemplateForTable, isCountriesInput, inputHasUser<PERSON>ield<PERSON>ey } from "../../../utils/inputUtils";
import { getAbpLanguage, getLocaleName } from "../../../utils/languageUtils";
import { analyzeAttributeDependencies, conditionalAttribute, createInputsStateKey, getConditionalResultFromAttrJsonProps, renderElement } from "../../../utils/policyCalculationUtils";
import { generateDropdownOptionsIfCountriesInput } from "../../../utils/storeUtils";
import { filterBySome, isJsonString } from "../../../utils/utils";
import { ContentViewModelProperty } from "../../BaseComponents/contentViewBase";
import { Controls } from "../../BaseComponents/controls";
import { VehicleListBase } from "../../BaseComponents/vehicleListBase";
import { EurotaxExpertInfoMixedCustomInputsBox } from "./eurotaxExpertInfoMixedCustomInputsBox";
import travelCountryCoverageService from "../../../services/travelCountryCoverage/travelCountryCoverageService";
import { Card, CardHeader } from "@fluentui/react-card";

var _ = require('lodash');

const cancelIcon: IIconProps = { iconName: 'Cancel' };

const iconButtonStyles: Partial<IButtonStyles> = {
    root: {
        color: myTheme.palette.neutralPrimary,
        marginLeft: 'auto',
        marginTop: '4px',
        marginRight: '2px',
    },
    rootHovered: {
        color: myTheme.palette.neutralDark,
    },
};

const classNames = mergeStyleSets({
    hide: {
        display: 'none !important',
    },
    toolbar: {
        selectors: {
            '& .ms-Pivot': {
                display: 'none',
            }
        }
    },
    pivotItemButton: {
        selectors: {
            '& .ms-Pivot-icon': {
                color: 'red',
                fontSize: '13px',
                marginRight: '3px',
                selectors: {
                    '& i': {
                        fontWeight: 'bold',
                    }
                }
            }
        }
    },
    messageBar: {
        width: 'fit-content'
    },
    loadSpinner: {
        display: 'inline-flex',
        marginTop: '15px',
        marginLeft: 'auto',
        marginRight: 'auto',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    loadSpinnerTopBar: {
        display: 'inline-flex',
        marginLeft: '15px',
        marginRight: 'auto',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    toggleSlider: {
        margin: '15px 0',
        display: 'flex',
        alignContent: 'center',
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        border: `1px solid ${myTheme.palette.neutralSecondaryAlt}`,
        padding: '1px 16px',
        borderRadius: '2px',
        transition: 'background 150ms',
        background: myTheme.palette.white,
        selectors: {
            '& .ms-Slider-container': {
                cursor: 'grab',
                width: '40px',
                marginLeft: '10px',
            }
        }
    },
    toggleSliderActive: {
        background: myTheme.palette.themeLight,
    },
    container: {
        display: 'flex',
        flexFlow: 'column nowrap',
        alignItems: 'stretch',
        justifyContent: 'center',
        width: '75%',
        overflowX: 'hidden',
    },
    header: [
        myTheme.fonts.large,
        {
            flex: '1 1 auto',
            color: myTheme.palette.neutralPrimary,
            display: 'flex',
            alignItems: 'center',
            fontWeight: FontWeights.semibold,
            padding: '12px 12px 14px 24px',
        },
    ],
    contentContainer: {
        width: '100%',
        height: 'auto',
        padding: '0 20px 40px 20px',
        boxSizing: 'border-box',
        selectors: {
            '> div': {
                marginRight: '0',
                width: '99.9%',
                maxWidth: '99.9%',
                selectors: {
                    '> div': {
                        width: '99.9%',
                        maxWidth: '99.9%',
                    }
                }
            }
        }
    },
    pivotItemsContainer: {
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        marginTop: '30px !important',
        marginBottom: '20px',
        minWidth: '30%',
        maxWidth: '1200px',
        minHeight: '150px',
        padding: '35px',
        boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.25)',
        borderRadius: '12px',
        selectors: {
            ':first-child': {
                marginTop: '25px',
            }
        }
    },
    modalActionButton: {
        display: 'block',
        margin: '30px auto 0',
    },
    myTabItem: {
        display: 'flex'
        // '& div > div:first-child': {
        //     width: '320px !important',
        //     minWidth: '320px !important',
        //     marginRight: '50px !important'
        // },
        // '& div.checkboxLabel-62': {
        //     width: 'auto !important',
        // },
        // '& div.checkboxOptionsWrapper-64 div': {
        //     width: 'auto !important',
        //     minWidth: 'auto !important',
        //     marginLeft: '6px !important',
        // }
    },
    vehicleConfigurationCard: {
        position: 'absolute !important',
        right: '25px',
        maxWidth: '30%',
        top: '50px',
        paddingRight: '15px !important',
        minWidth: '300px',
        selectors: {
            'ul': {
                paddingLeft: '20px !important',
            }
        }
    }
});

export interface IPolicyCalculationStep2Props {
    productAttributes: any;
    countryStore: any;
    travelCountryStore: any;
    sportDisciplineStore: any;
    vehicleStore: any;
    product: any;
    isDataLoaded: boolean;
    inputsTypeValuePairs: any;
    inputsIdUserFieldsPairs: any;
    gnLanguage: any;
    customInputsData: any;
    isFastCalculation: boolean;
    inputErrors: number;
    allUserFields: any[];
    isEditMode: boolean;
    tempSelectedVehicle: string;
    inputsChangedManually: any;
    inputValuePairsStringified: string;
    asyncActionInProgress?: boolean;
    savedMappedIdsForLaterUse: any;
    customPresettedInputOptions: any;
    inputErrorsText: any;
    savedTemplateInputsForTable: any;
    hiddenInputsTypeValuePairs: any;
    changeLoadSpinnerLabel: (newLabel: string | null) => void;
    changeAdjustInputsChangedManually: (id: string, value: any) => void;
    setInputErrors: (errorsCount: number) => void;
    setIsFastCalculation: (value: boolean) => void;
    saveInputsForCalculationAdjust: (inputs: any) => void;
    onInputChange: (id: string, value: any, userFields: any) => void;
    setEurotaxInfoexpertFormData: (id: string, value: string) => void;
    onMassInputChange: (inputFields: any, userFields: any) => void;
    toggleAsyncActionFlag: (newState: boolean, forceUpdate: boolean) => void;
    mapKeyToId: (mapType: string, key: string) => string;
    onTempVehicleSelect: (tempVehicle: any, justId: boolean) => void;
    formTabSwitch: (func: () => void) => void;
    onTabChange?: (selectedKey: string) => void;
    setInputsUserFields: (id: string, userFields: any) => void;
    setSavedTemplateInputsForTable: (value: any) => void;
    setHiddenInputsTypeValuePairs: (id: string, value: any) => void;
}

export class PolicyCalculationStep2 extends React.Component<IPolicyCalculationStep2Props> {
    private tabKeyIndexHelper: any = {};
    private tabs: any[] = [
        // { key: UserField.Key, name: displayName, items: [{value, order}, {value, order}] }
    ];
    private tabsParsed: boolean = false;
    private tabsSelectedKey: string = '0';
    private initialReRender: boolean = false;
    private inputsToSet: any[] = [];
    private showExpertInfoCustomInputsBox: boolean = false;
    private showEurotaxCustomInputsBox: boolean = false;
    private showEurotaxExpertInfoMixedCustomInputsBox: boolean = this.props.isEditMode === true ? true : false;
    // private fillingExampleDataActionInProgress: boolean = false;
    private exampleDataLoadedFlag: boolean = false;
    private templateInputsForTable: any = {};
    private templateInputsForCalculationAdjust: any = {};
    private tempInputIdUserFieldPairs: any = {};
    private prevTempInputErrors: number = 0;
    private tempInputErrors: number = 0;
    private tabInputsInEditModeSet: boolean = false;
    private isVehicleListModalOpen: boolean = false;
    private lastRenderTimestamp: number = 0;
    private lastSetDefaultValueTimestamp: number = 0;
    private conditionalDefaultValueAlreadySetFor: any[] = [];
    private savedAttributes: any = {};
    private prevInputValuePairsStringified: string = '';
    private prevInputErrorsTextStringified: string = '';
    private prevCustomInputsDataStringified: string = '';
    private prevAsyncActionInprogress: boolean | undefined = undefined;
    private selectCountryMappedInputId: string = '';
    private prevConditionalOptionsResult: any = {};
    private showButton: boolean = true;
    private savedProductAttributesForId: any = {};
    private onTabChange?: (selectedKey: string) => void;
    private _countryListSelection: Selection = new Selection({
        onSelectionChanged: async () => {
            const selectedCountry: any = this._countryListSelection.getSelection();

            if(selectedCountry && Array.isArray(selectedCountry) && selectedCountry.length > 0 && !!selectedCountry[0].id) {
                this.props.toggleAsyncActionFlag(true, true);

                let tempInsurerGeoZonePairs: any[] = [];
                await travelCountryCoverageService.getByCountryId(selectedCountry[0].id).then((response: any) => {
                    if(response && response.items.length > 0) {
                        for(let key in response.items) {
                            if(response.items.hasOwnProperty(key)) {
                                const item: any = response.items[key];
                                if(!!item.insurerName && !!item.geographicalZone) {
                                    tempInsurerGeoZonePairs.push({...item});
                                }
                            }
                        };
                    }
                }).catch((error: any) => {
                    console.error(error);
                });

                this.props.toggleAsyncActionFlag(false, true);

                selectedCountry[0]['insurerGeographicalZonePairs'] = tempInsurerGeoZonePairs;

                const currentValue = this.props.inputsTypeValuePairs[this.selectCountryMappedInputId] && isJsonString(this.props.inputsTypeValuePairs[this.selectCountryMappedInputId]) && Array.isArray(JSON.parse(this.props.inputsTypeValuePairs[this.selectCountryMappedInputId])) ?
                                        JSON.parse(this.props.inputsTypeValuePairs[this.selectCountryMappedInputId]) : this.props.inputsTypeValuePairs[this.selectCountryMappedInputId];
                const newObj: any[] = currentValue ? [...currentValue] : [];
                const newObjNames: string[] = newObj.map((country: any) => country.name);

                if(!newObjNames.includes(selectedCountry[0].name)) {
                    this.props.onInputChange(this.selectCountryMappedInputId, JSON.stringify([...newObj, selectedCountry[0]]), 
                                                this.tempInputIdUserFieldPairs && this.tempInputIdUserFieldPairs[this.selectCountryMappedInputId]);
                }

                this._countryListSelection.setAllSelected(false);
            }
            this.forceUpdate();
        },
        selectionMode: SelectionMode.single
    });
    private selectSportMappedInputId: string = '';
    private _sportListSelection: Selection = new Selection({
        onSelectionChanged: () => {
            const selectedSport: any = this._sportListSelection.getSelection();
            if (Array.isArray(selectedSport) && selectedSport.length > 0 && selectedSport[0].id) {
                if (this.selectSportMappedInputId.length === 0) this.selectSportMappedInputId = this.props.mapKeyToId("mapAttributeNameToId", "travelSportList");
                const currentValue = this.props.inputsTypeValuePairs[this.selectSportMappedInputId] && isJsonString(this.props.inputsTypeValuePairs[this.selectSportMappedInputId]) && Array.isArray(JSON.parse(this.props.inputsTypeValuePairs[this.selectSportMappedInputId])) ?
                    JSON.parse(this.props.inputsTypeValuePairs[this.selectSportMappedInputId]) : this.props.inputsTypeValuePairs[this.selectSportMappedInputId];
                const newObj: any[] = currentValue ? [...currentValue] : [];
                const newObjIds: number[] = newObj.map((sport: any) => sport.id);
    
                if (!newObjIds.includes(selectedSport[0].id) && newObj.length < 4) {
                    // for multiple values: JSON.stringify([...newObj, selectedSport[0]])
                    this.props.onInputChange(
                        this.selectSportMappedInputId,
                        JSON.stringify([...newObj, selectedSport[0]]),
                        this.tempInputIdUserFieldPairs && this.tempInputIdUserFieldPairs[this.selectSportMappedInputId]
                    );
                }
                this._sportListSelection.setAllSelected(false);
            }
            this.forceUpdate();
        },
        selectionMode: SelectionMode.single
    });
    
    private debouncedOnInputChange: any = _.debounce((inputKey: string, value: any, userFields: any, customPayload?: any) => {
        this.props.onInputChange(inputKey, value, userFields);

        this.props.changeAdjustInputsChangedManually(inputKey, value);
    }, AppConsts.defaultInputsDelay, []);

    // private startTime: number = Date.now();
    // private prevMsDiff: number = 0;
    // private msDiffCount: number[] = [];
    private conditionalAttributeCache: Map<string, any> = new Map();
    private inputDependencies: Map<string, Set<string>> = new Map();
    private keyIdPairs: Map<string, string> = new Map();

    componentDidMount() {
        this.setDefaultInputsData();

        this.selectCountryMappedInputId = this.props.mapKeyToId("mapAttributeNameToId", "travelCountryList");

        if(!this.initialReRender) {
            this.initialReRender = true;
            this.forceUpdate();
        }

        this.conditionalAttributeCache = new Map();
        this.inputDependencies = new Map();

        this.props.formTabSwitch(this.handleTabSwitch);
    }

    componentDidUpdate(prevProps: any) {
        if (prevProps.product?.Id !== this.props.product?.Id) {
            this.conditionalAttributeCache = new Map();
            this.inputDependencies = new Map();
        }
    }

    private setDefaultInputsData() {
        if(this.inputsToSet.length > 0) {
            const cloneInputsTypeValuePairs = {};
            const cloneInputsIdUserFieldsPairs = {};
            
            this.inputsToSet.forEach((element) => {
                // this.props.onInputChange(element.id, element.value, this.tempInputIdUserFieldPairs[element.id]);
                cloneInputsTypeValuePairs[element.id] = element.value ? 
                                                        (isJsonString(element.value) ? 
                                                            JSON.parse(element.value) : element.value) 
                                                        : element.value;
                cloneInputsIdUserFieldsPairs[element.id] = this.tempInputIdUserFieldPairs[element.id];
            });

            this.props.onMassInputChange(cloneInputsTypeValuePairs, cloneInputsIdUserFieldsPairs);
            this.inputsToSet = [];
        }
    }

    private setConditionalDefaultInputsData(inputsToSet: any[]) {
        if(inputsToSet.length > 0) {
            inputsToSet.forEach((element) => {
                if(this.props.inputsChangedManually[element.id]) {
                    return;
                }
                this.props.onInputChange(element.id, element.value, element.userFields);
            });
            inputsToSet = [];
        }
    }

    // private async fillWithExampleData() {
    //     this.props.toggleAsyncActionFlag(true, true);
    //     this.fillingExampleDataActionInProgress = true;

    //     let type: string = "";

    //     if(this.props.product && this.props.product.SeName && this.props.product.SeName.length > 0) {
    //         type = this.props.mapKeyToId("mapProductIdToType", this.props.mapKeyToId("mapProductNameToProductId", this.props.product.SeName));

    //         if(type.length > 0) {
    //             await policyCalculationService.getFillExample(type).then((response) => {
    //                 if(response.data && response.data.data && response.data.data.length > 0) {
    //                     const exampleData = response.data.data;
    //                     const cloneInputsTypeValuePairs = {...this.props.inputsTypeValuePairs};
    //                     const cloneInputsIdUserFieldsPairs = {...this.props.inputsIdUserFieldsPairs};

    //                     for(let key in exampleData) {
    //                         if(exampleData.hasOwnProperty(key)) {
    //                             cloneInputsTypeValuePairs[exampleData[key].productAttributeId] = exampleData[key].valueId ? 
    //                                                                                 (isJsonString(exampleData[key].valueId) ? 
    //                                                                                     JSON.parse(exampleData[key].valueId) : exampleData[key].valueId) 
    //                                                                                 : exampleData[key].value;
    //                             cloneInputsIdUserFieldsPairs[exampleData[key].productAttributeId] = this.tempInputIdUserFieldPairs[exampleData[key].productAttributeId];                                            
    //                         }
    //                     }

    //                     this.props.onMassInputChange(cloneInputsTypeValuePairs, cloneInputsIdUserFieldsPairs);
    //                 }
    //             });
    //         }

    //         this.exampleDataLoadedFlag = true;
    //         this.fillingExampleDataActionInProgress = false;
    //         this.props.toggleAsyncActionFlag(false, true);
    //     }
    // }

    private setTabs(UserFields: any) {
        let tabKeys: string[] = [];
        let tabNames: string[] = [];

        UserFields.forEach((UserField: any) => {    
            if(UserField.Key === "tabsName") {
                tabNames = UserField.Value.split(';;');

                tabNames.forEach((tabName, i) => {
                    let splittedTabNames = tabName.split(';');
                    splittedTabNames.forEach((splittedTabName) => {
                        let translatedNames = splittedTabName.split('=');
                        if(translatedNames[0].toLowerCase() === getAbpLanguage()) {
                            tabNames[i] = translatedNames[1];
                        }
                    });
                });
            } else if(UserField.Key === "tabs") {
                tabKeys = UserField.Value.split(';');
                if(tabNames.length <= 0) {
                    tabNames = tabKeys;
                }
            }
            
        });
        
        for(let i: number = 0; i < tabKeys.length; i++) {
            if(tabKeys[i].length > 0) {
                this.tabs.push({ key: tabKeys[i], name: tabNames[i], items: [], tabErrorsCount: 0});
                this.tabKeyIndexHelper[tabKeys[i]] = this.tabs.length - 1;
            }
        };
    }

    private parseTabs(UserFields: any, attributeElement: any, attrId: string, attributeId: string, updateValue: boolean, tabHasError?: boolean) {
        let tabUserField: any = {};
        let hideAttr: boolean = false;

        UserFields.forEach((UserField: any) => {
            if(UserField.Key === "Hide" && UserField.Value === "true") {
                hideAttr = true;
            }

            if(UserField.Key.substr(0, 4) === "step" && UserField.Key.substr(0, 5) !== "step_") {
                tabUserField = UserField;
            }
        });

        if(Number.isInteger(this.tabKeyIndexHelper[tabUserField.Key])) {
            const currentTabErrorCount: number = this.tabs[this.tabKeyIndexHelper[tabUserField.Key]].tabErrorsCount;
            let newTabErrorCount: number = tabHasError === true ? currentTabErrorCount + 1 : currentTabErrorCount;
            this.tabs[this.tabKeyIndexHelper[tabUserField.Key]].tabErrorsCount = newTabErrorCount;
        }

        if(Number.isInteger(this.tabKeyIndexHelper[tabUserField.Key]) && (!hideAttr || this.props.isEditMode) && typeof attributeElement !== 'undefined') {
            if(updateValue) {
                let attrUpdated = false;
                this.tabs[this.tabKeyIndexHelper[tabUserField.Key]].items.some((item: any) => {
                    if(item.id === attributeId) {
                        item.value = attributeElement;
                        attrUpdated = true;
                        return true;
                    }
                    return false;
                });

                if(!attrUpdated) {
                    this.tabs[this.tabKeyIndexHelper[tabUserField.Key]].items.push({ id: attributeId, value: attributeElement, order: tabUserField.Value, hide: hideAttr });
                }
            } else {
                this.tabs[this.tabKeyIndexHelper[tabUserField.Key]].items.push({ id: attributeId, value: attributeElement, order: tabUserField.Value, hide: hideAttr });
            }
        } else if(!attributeElement) {
            let indexToDelete = -1;

            this.tabs[this.tabKeyIndexHelper[tabUserField.Key]].items.some((item: any, index: number) => {
                if(item.id === attributeId) {
                    indexToDelete = index;
                    return true;
                }
                return false;
            });

            if(indexToDelete >= 0) {
                delete this.tabs[this.tabKeyIndexHelper[tabUserField.Key]].items[indexToDelete];
                delete this.props.inputsTypeValuePairs[attrId];
            }
        }
        // else { ### TODO create additional L("Other") tab and store there attributes without tab Key
        //     this.tabs.push({ name: UserField.Key, items: [{ value: attributeElement, order: UserField.Value }]});
        //     this.tabKeyIndexHelper[UserField.Key] = this.tabs.length - 1;
        // }
    }

    private deleteFromTab(ProductAttributeId: string) {
        if(this.tabs && this.tabs.length > 0) {
            this.tabs.some((tab: any, tabIndex: number) => {
                let itemFound: boolean = false;

                if(tab.items) {
                    tab.items.some((item: any, itemIndex: number) => {
                        if(item.id === ProductAttributeId) {
                            this.tabs[tabIndex].items.splice(itemIndex, 1);
                            itemFound = true;
                            return true;
                        }
                        return false;
                    });
                }

                if(itemFound) {
                    this.tabs[tabIndex].tabErrorsCount = this.tabs[tabIndex].tabErrorsCount > 0 ? this.tabs[tabIndex].tabErrorsCount - 1 : 0;
                    return true;
                }
                return false;
            });
        }
    }

    private sortByOrder(a: any, b: any) {
        a.order = parseInt(a.order);
        b.order = parseInt(b.order);

        if (a.order < b.order){
            return -1;
        }
        if (a.order > b.order){
            return 1;
        }
        return 0;
    }

    private toggleEurotaxExpertInfoMixedCustomInputsBox() {
        this.showEurotaxExpertInfoMixedCustomInputsBox = !this.showEurotaxExpertInfoMixedCustomInputsBox;
        if(this.showEurotaxExpertInfoMixedCustomInputsBox === true) {
            this.showEurotaxCustomInputsBox = false;
            this.showExpertInfoCustomInputsBox = false;
        }
        this.forceUpdate();
    }

    private toggleExpertInfoCustomInputsBox() {
        this.showExpertInfoCustomInputsBox = !this.showExpertInfoCustomInputsBox;
        if(this.showExpertInfoCustomInputsBox === true) {
            this.showEurotaxCustomInputsBox = false;
            this.showEurotaxExpertInfoMixedCustomInputsBox = false;
        }
        this.forceUpdate();
    }

    private toggleEurotaxCustomInputsBox() {
        this.showEurotaxCustomInputsBox = !this.showEurotaxCustomInputsBox;
        if(this.showEurotaxCustomInputsBox === true) {
            this.showExpertInfoCustomInputsBox = false;
            this.showEurotaxExpertInfoMixedCustomInputsBox = false;
        }
        this.forceUpdate();
    }

    private toggleVehicleListModal() {
        this.isVehicleListModalOpen = !this.isVehicleListModalOpen;
        this.forceUpdate();
    }

    private travelIncreaseNumberOfTravelers() {
        const travelTypeOfCalculationId = this.props.mapKeyToId("mapAttributeNameToId", "travelTypeOfCalculation");
        const NumberOfPeopleTravelingId: string = typeof this.props.inputsTypeValuePairs[travelTypeOfCalculationId] === 'undefined' ||
                    this.props.inputsTypeValuePairs[travelTypeOfCalculationId] === this.props.mapKeyToId("mapAttributeValueToOptionId", "TYPE_OF_CALCULATION_LIMITED") ? 
                    this.props.mapKeyToId("mapAttributeNameToId", "numberOfPeopleTraveling") : 
                    this.props.mapKeyToId("mapAttributeNameToId", "numberOfPeopleTravelingFullData");

        if(!!NumberOfPeopleTravelingId) {
            const currentValue: string | undefined = this.props.inputsTypeValuePairs[NumberOfPeopleTravelingId];
            if(currentValue && !!currentValue) {
                this.props.onInputChange(NumberOfPeopleTravelingId, (parseInt(currentValue) + 1).toString(), this.props.inputsIdUserFieldsPairs[NumberOfPeopleTravelingId]);
            } else {
                this.props.onInputChange(NumberOfPeopleTravelingId, '1', this.props.inputsIdUserFieldsPairs[NumberOfPeopleTravelingId]);
            }
        }
    }

    private travelReduceNumberOfTravelers() {
        const travelTypeOfCalculationId = this.props.mapKeyToId("mapAttributeNameToId", "travelTypeOfCalculation");
        const NumberOfPeopleTravelingId: string = typeof this.props.inputsTypeValuePairs[travelTypeOfCalculationId] === 'undefined' ||
                    this.props.inputsTypeValuePairs[travelTypeOfCalculationId] === this.props.mapKeyToId("mapAttributeValueToOptionId", "TYPE_OF_CALCULATION_LIMITED") ? 
                    this.props.mapKeyToId("mapAttributeNameToId", "numberOfPeopleTraveling") : 
                    this.props.mapKeyToId("mapAttributeNameToId", "numberOfPeopleTravelingFullData");

        if(!!NumberOfPeopleTravelingId) {
            const currentValue: string | undefined = this.props.inputsTypeValuePairs[NumberOfPeopleTravelingId];
            if(currentValue && !!currentValue) {
                this.props.onInputChange(NumberOfPeopleTravelingId, parseInt(currentValue) > 0 ? (parseInt(currentValue) - 1).toString() : '0', this.props.inputsIdUserFieldsPairs[NumberOfPeopleTravelingId]);
            } else {
                this.props.onInputChange(NumberOfPeopleTravelingId, '0', this.props.inputsIdUserFieldsPairs[NumberOfPeopleTravelingId]);
            }
        }
    }

    private cancelTravelIncreaseNumberOfTravelers() {
        const travelTypeOfCalculationId = this.props.mapKeyToId("mapAttributeNameToId", "cancelTravelTypeOfCalculation");
        const NumberOfPeopleTravelingId: string = typeof this.props.inputsTypeValuePairs[travelTypeOfCalculationId] === 'undefined' ||
                    this.props.inputsTypeValuePairs[travelTypeOfCalculationId] === this.props.mapKeyToId("mapAttributeValueToOptionId", "CANCEL_TRAVEL_TYPE_OF_CALCULATION_LIMITED") ? 
                    this.props.mapKeyToId("mapAttributeNameToId", "cancelNumberOfPeopleTraveling") : 
                    this.props.mapKeyToId("mapAttributeNameToId", "cancelNumberOfPeopleTraveling"); // cancelNumberOfPeopleTravelingFullData

        if(!!NumberOfPeopleTravelingId) {
            const currentValue: string | undefined = this.props.inputsTypeValuePairs[NumberOfPeopleTravelingId];
            if(currentValue && !!currentValue) {
                this.props.onInputChange(NumberOfPeopleTravelingId, (parseInt(currentValue) + 1).toString(), this.props.inputsIdUserFieldsPairs[NumberOfPeopleTravelingId]);
            } else {
                this.props.onInputChange(NumberOfPeopleTravelingId, '1', this.props.inputsIdUserFieldsPairs[NumberOfPeopleTravelingId]);
            }
        }
    }

    private cancelTravelReduceNumberOfTravelers() {
        const travelTypeOfCalculationId = this.props.mapKeyToId("mapAttributeNameToId", "cancelTravelTypeOfCalculation");
        const NumberOfPeopleTravelingId: string = typeof this.props.inputsTypeValuePairs[travelTypeOfCalculationId] === 'undefined' ||
                    this.props.inputsTypeValuePairs[travelTypeOfCalculationId] === this.props.mapKeyToId("mapAttributeValueToOptionId", "CANCEL_TRAVEL_TYPE_OF_CALCULATION_LIMITED") ? 
                    this.props.mapKeyToId("mapAttributeNameToId", "cancelNumberOfPeopleTraveling") : 
                    this.props.mapKeyToId("mapAttributeNameToId", "cancelNumberOfPeopleTraveling"); // cancelNumberOfPeopleTravelingFullData

        if(!!NumberOfPeopleTravelingId) {
            const currentValue: string | undefined = this.props.inputsTypeValuePairs[NumberOfPeopleTravelingId];
            if(currentValue && !!currentValue) {
                this.props.onInputChange(NumberOfPeopleTravelingId, parseInt(currentValue) > 0 ? (parseInt(currentValue) - 1).toString() : '0', this.props.inputsIdUserFieldsPairs[NumberOfPeopleTravelingId]);
            } else {
                this.props.onInputChange(NumberOfPeopleTravelingId, '0', this.props.inputsIdUserFieldsPairs[NumberOfPeopleTravelingId]);
            }
        }
    }

    private mapAttributes(): any { 
        const {inputsTypeValuePairs, inputsIdUserFieldsPairs, productAttributes, product, isDataLoaded, gnLanguage, savedMappedIdsForLaterUse, customPresettedInputOptions, customInputsData} = this.props;

        this.tempInputErrors = 0;

        let shouldMassUpdateInputs: boolean = false;
        let inputsToSet: any[] = [];
        let now = + new Date();

        let attributes: any = (product && product.ProductAttributeMappings ? product.ProductAttributeMappings!.map((attr: any, i: number) => {             
            let skipRender: boolean = false;
            let control: any;
            let options: any = {
                dropdown: [] as IDropdownOption[],
                choicegroup: [] as IChoiceGroupOption[],
                tableInputs: {} as any,
            };
            let textFieldRows = 1;
            let tempUserFields: any = null;
            let attribute: any = attr.ProductAttribute;
            let customControlTypeUserField: any;
            let conditionalOptionsResult: any = {};
            let tempDefaultValue: string = '';

            this.prevTempInputErrors = this.tempInputErrors;

            attribute['attrId'] = attr.Id;

            if(attribute.UserFields) {
                tempUserFields = attribute.UserFields;
                customControlTypeUserField = tempUserFields.filter((userField: any) => userField.Key === 'customControlType');
                
                if(inputHasUserFieldKey(tempUserFields, "is_only_for_apk")) {
                    return false;
                }
                
                const itemsAttrJsonPropsUserField: any = filterBySome(tempUserFields, "Key", "items_attr_json_props");
                if(itemsAttrJsonPropsUserField && itemsAttrJsonPropsUserField.Value) {
                    const parsedItemsAttrJsonPropsUserField: any = JSON.parse(itemsAttrJsonPropsUserField.Value);
                    for(let optionName in parsedItemsAttrJsonPropsUserField) {
                        conditionalOptionsResult[optionName] = getConditionalResultFromAttrJsonProps(parsedItemsAttrJsonPropsUserField[optionName], this.props.inputsTypeValuePairs, 
                                                    this.props.product.ProductAttributeMappings, productAttributes, this.props.gnLanguage, true);
                    }
                }
            }

            if(attr.DefaultValue && !inputsTypeValuePairs[attr.Id]) {
                this.inputsToSet.push({id: attr.Id, value: attr.DefaultValue});
                tempDefaultValue = attr.DefaultValue;
            } else if(!!tempUserFields) {
                const defaultValueUserField: any = filterBySome(tempUserFields, "Key", "defaultValue");
                if(defaultValueUserField && defaultValueUserField.Value && !inputsTypeValuePairs[attr.Id]) {
                    this.inputsToSet.push({id: attr.Id, value: defaultValueUserField.Value});
                    tempDefaultValue = defaultValueUserField.Value;
                }
            }

            let controlType: string = customControlTypeUserField && customControlTypeUserField.length > 0 ? customControlTypeUserField[0].Value : attr.AttributeControlTypeId;
            switch(controlType) {
                case "Datepicker":
                    control = Controls.Date;
                    break;
                case "Timepicker":
                    control = Controls.Time;
                    break;
                case "TextBox":
                    control = Controls.Text;
                    break;
                case "MultilineTextbox":
                    control = Controls.Text;
                    textFieldRows = 5;
                    break;
                case "RadioList":
                    control = Controls.ChoiceGroup;
                    options.choicegroup = attr.ProductAttributeValues.map((attrValue: any) => {
                        if(!inputsTypeValuePairs[attr.Id] && !this.inputsToSet.some(e => e.id === attr.Id) && attrValue.IsPreSelected && !this.tabsParsed) {
                            this.inputsToSet.push({id: attr.Id, value: attrValue.Id});
                        }

                        return { key: attrValue.Id, name: attrValue.Name,
                                text: attrValue.Locales && attrValue.Locales.length > 0 ? getLocaleName(attrValue.Locales, gnLanguage) : attrValue.Name, 
                                checked: inputsTypeValuePairs[attr.Id] && inputsTypeValuePairs[attr.Id] === attrValue.Id ? true : attrValue.IsPreSelected,
                                disabled: typeof conditionalOptionsResult[attrValue.Name] !== 'undefined' ? conditionalOptionsResult[attrValue.Name].disabled : false,
                                hidden: typeof conditionalOptionsResult[attrValue.Name] !== 'undefined' ? !conditionalOptionsResult[attrValue.Name].show : false }
                    }) as IChoiceGroupOption[];
                    break;
                case "Checkboxes":
                    control = Controls.CheckBoxOptions;
                    options.dropdown = attr.ProductAttributeValues.map((attrValue: any) => {
                        if(!inputsTypeValuePairs[attr.Id] && !this.inputsToSet.some(e => e.id === attr.Id) && attrValue.IsPreSelected && !this.tabsParsed) {
                            this.inputsToSet.push({id: attr.Id, value: attrValue.Id});
                        }

                        return { key: attrValue.Id, name: attrValue.Name,
                                text: attrValue.Locales && attrValue.Locales.length > 0 ? getLocaleName(attrValue.Locales, gnLanguage) : attrValue.Name, 
                                isSelected: inputsTypeValuePairs[attr.Id] ? inputsTypeValuePairs[attr.Id][attrValue.Id] : attrValue.IsPreSelected,
                                disabled: typeof conditionalOptionsResult[attrValue.Name] !== 'undefined' ? conditionalOptionsResult[attrValue.Name].disabled : false,
                                hidden: typeof conditionalOptionsResult[attrValue.Name] !== 'undefined' ? !conditionalOptionsResult[attrValue.Name].show : false };
                    }) as IDropdownOption[];
                    break;
                case "DropdownList":
                case "MultiDropdownList":
                    control = controlType === 'MultiDropdownList' ? Controls.MultiPicker : Controls.Picker;
                    options.dropdown = attr.ProductAttributeValues.map((attrValue: any) => {
                        if(!inputsTypeValuePairs[attr.Id] && !this.inputsToSet.some(e => e.id === attr.Id) && attrValue.IsPreSelected && !this.tabsParsed) {
                            this.inputsToSet.push({id: attr.Id, value: attrValue.Id});
                        }
                        return { key: attrValue.Id, name: attrValue.Name,
                                text: attrValue.Locales && attrValue.Locales.length > 0 ? getLocaleName(attrValue.Locales, gnLanguage) : attrValue.Name, 
                                isSelected: attrValue.IsPreSelected,
                                disabled: typeof conditionalOptionsResult[attrValue.Name] !== 'undefined' ? conditionalOptionsResult[attrValue.Name].disabled : false,
                                hidden: typeof conditionalOptionsResult[attrValue.Name] !== 'undefined' ? !conditionalOptionsResult[attrValue.Name].show : false };
                    }) as IDropdownOption[];
                    break;
                case "ColorSquares":
                case "Table":
                    control = Controls.TableInputs;

                    if(typeof inputsTypeValuePairs[attr.Id] === 'undefined') {
                        this.inputsToSet.push({id: attr.Id, value: ''});
                    }

                    if(this.exampleDataLoadedFlag) {
                        skipRender = true;
                        this.exampleDataLoadedFlag = false;
                    }
                    break;
                case "CountrySearchList":
                    control = Controls.CountrySearchList;
                    break;
                case "SportSearchList":
                    control = Controls.SportSearchList;
                    break;
            }

            let attrName: string = "";
            let iconData: any = {};
            let validationData: any = {};
            let isInTab: boolean = false;
            let tabHasError: boolean = false;
            let isFastCalculation: boolean = false;
            let saveAsTemplateForTable: string | boolean = false;
            let saveAsTemplateForCalculationAdjust: boolean | string = false;

            validationData = getInputValidationData(attribute, customInputsData);

            if(!!tempDefaultValue) {
                validationData['defaultValue'] = tempDefaultValue;
            }

            if(control === Controls.CountrySearchList) {
                validationData['disableGetAllOnMount'] = true;
            }

            if(attr.Id === this.props.savedMappedIdsForLaterUse.travelEndDateId && !!inputsTypeValuePairs[this.props.savedMappedIdsForLaterUse.travelStartDateId]) {
                const tempStartDate: Date = new Date(inputsTypeValuePairs[this.props.savedMappedIdsForLaterUse.travelStartDateId]);
                let newEndDate: Date = addYears(tempStartDate, 1);
                newEndDate.setDate(newEndDate.getDate() - 1);
                validationData['maxDate'] = newEndDate;
            }

            if(attribute.UserFields) {
                isInTab = isInputInTab(attribute.UserFields);
                isFastCalculation = isInputFastCalculation(attribute.UserFields);
                saveAsTemplateForTable = shouldSaveAsTemplateForTable(attr.Id, attribute.UserFields, this.templateInputsForTable, productAttributes, this.props.allUserFields, product);
                if(!this.templateInputsForCalculationAdjust[attr.Id] || this.prevConditionalOptionsResult[attr.Id] !== JSON.stringify(conditionalOptionsResult)) {
                    saveAsTemplateForCalculationAdjust = shouldSaveAsTemplateForCalculationAdjust(attr.Id, attribute.UserFields);
                    this.prevConditionalOptionsResult[attr.Id] = JSON.stringify(conditionalOptionsResult);
                }
            }

            if(attribute.Locales) {
                attrName = getLocaleName(attribute.Locales, gnLanguage);
            } else {
                attrName = attribute.Name;
            }

            if(controlType === "TextBox" || controlType === "ColorSquares" || controlType === "Table" || controlType === "DropdownList") {
                iconData = getInputIconData(attribute);
            }

            if(controlType === "DropdownList") {
                let dropdownOptions: IDropdownOption[] = [];
                if(isCountriesInput(attr)) {
                    dropdownOptions = getDropdownOptionsFromDataSource(attribute, this.props, "", this.props.gnLanguage);
                } else {
                    dropdownOptions = getDropdownOptionsFromDataSource(attribute, this.props, "Name", this.props.gnLanguage);
                }

                if(dropdownOptions && dropdownOptions.length > 0) {
                    options.dropdown = generateDropdownOptionsIfCountriesInput(attr, dropdownOptions);
                }
            }

            if(controlType === "ColorSquares" || controlType === "Table") {
                const tableOptions = getInputTableData(attribute, this.props.allUserFields, productAttributes, product);
                if(Object.keys(tableOptions).length > 0)
                    options.tableInputs = tableOptions;
            }

            if((attr.IsRequired || validationData.required) && (!inputsTypeValuePairs[attr.Id] || inputsTypeValuePairs[attr.Id].length === 0)) {
                tabHasError = true;
                this.tempInputErrors++;
            }

            if([savedMappedIdsForLaterUse.autoDrivingLicenceIssueYear, savedMappedIdsForLaterUse.autoYearOfPurchaseOfTheVehicle].includes(attr.Id) && Array.isArray(customPresettedInputOptions[attr.Id])) {
                control = Controls.Picker;
                options.dropdown = customPresettedInputOptions[attr.Id];
            }

            const property = new ContentViewModelProperty(attr.Id, attrName, control, attr.IsRequired ? true : (validationData.required ? true : false), options, 
                                    (typeof this.props.asyncActionInProgress === 'boolean' && this.props.asyncActionInProgress === true) ? true : false, 
                                    { isDataLoaded: isDataLoaded, rows: (validationData.multiline ? validationData.multiline : textFieldRows),
                                    textType: (validationData.inputType ? validationData.inputType : "text"), validationData: validationData,
                                    tooltipText: validationData.tooltipText,
                                    additionalMethod: async (value: string, customPayload: any) => {
                                        this.props.changeAdjustInputsChangedManually(attr.Id, value);
                                    } }
                                );

            if(saveAsTemplateForTable !== false && typeof saveAsTemplateForTable === 'string') {
                if(!this.templateInputsForTable[saveAsTemplateForTable]) {
                    this.templateInputsForTable[saveAsTemplateForTable] = [];
                }
                this.templateInputsForTable[saveAsTemplateForTable].push(property);

                if(!this.props.savedTemplateInputsForTable || 
                    JSON.stringify(this.templateInputsForTable) !== JSON.stringify(this.props.savedTemplateInputsForTable)
                ) {
                    this.props.setSavedTemplateInputsForTable(this.templateInputsForTable);
                }
            } else if(this.props.savedTemplateInputsForTable) {
                this.templateInputsForTable = this.props.savedTemplateInputsForTable;
            }

            if(saveAsTemplateForCalculationAdjust !== false && typeof saveAsTemplateForCalculationAdjust === 'string') { // && !this.templateInputsForCalculationAdjust[saveAsTemplateForCalculationAdjust]) {
                this.templateInputsForCalculationAdjust[saveAsTemplateForCalculationAdjust] = {};
                this.templateInputsForCalculationAdjust[saveAsTemplateForCalculationAdjust]['attr'] = attr;
                this.templateInputsForCalculationAdjust[saveAsTemplateForCalculationAdjust]['property'] = property;
                this.templateInputsForCalculationAdjust[saveAsTemplateForCalculationAdjust]['userFields'] = tempUserFields;

                this.props.saveInputsForCalculationAdjust(this.templateInputsForCalculationAdjust);
            }

            if(attr.DisplayOrder < 0 || skipRender || (isFastCalculation && this.props.isFastCalculation)) { // attr hidden
                if(skipRender || (isFastCalculation && this.props.isFastCalculation)) {
                    this.deleteFromTab(attr.ProductAttributeId);
                }
                return false;
            }

            this.tempInputIdUserFieldPairs[property.id] = tempUserFields;
            
            let conditionalAttributeResult: any = this.getMemoizedConditionalAttribute(
                attr, inputsTypeValuePairs, {...inputsIdUserFieldsPairs, [property.id]: tempUserFields}, product.ProductAttributeMappings, productAttributes, gnLanguage
            );

            let valuesAreTheSame: boolean = false;
            if(typeof inputsTypeValuePairs[attr.Id] !== 'undefined' && typeof this.conditionalDefaultValueAlreadySetFor[attr.Id] !== 'undefined') {
                if(typeof inputsTypeValuePairs[attr.Id] !== 'object') {
                    valuesAreTheSame = inputsTypeValuePairs[attr.Id] === this.conditionalDefaultValueAlreadySetFor[attr.Id].settedValue;
                } else {
                    valuesAreTheSame = JSON.stringify(inputsTypeValuePairs[attr.Id]) === JSON.stringify(this.conditionalDefaultValueAlreadySetFor[attr.Id].settedValue);
                }
            }

            const nowMinusLastSetDefaultTimestamp: number = now - this.lastSetDefaultValueTimestamp;
            if(typeof conditionalAttributeResult.newValue !== 'undefined' && typeof conditionalAttributeResult.newValue === 'string' && 
                (!this.conditionalDefaultValueAlreadySetFor[attr.Id] || 
                    this.conditionalDefaultValueAlreadySetFor[attr.Id].value !== conditionalAttributeResult.newValue || 
                    ((nowMinusLastSetDefaultTimestamp >= 0 && nowMinusLastSetDefaultTimestamp < 50) && (conditionalAttributeResult.newValue.length > 0 && !valuesAreTheSame))
                )
            ) {
                let valueToSet: string = conditionalAttributeResult.newValue;
                let inputsToSetUpdated: boolean = false;

                if(property.type === Controls.ChoiceGroup || 
                    property.type === Controls.CheckBoxOptions
                ) {
                    if(valueToSet.length === 0) {
                        let optionsToSet: any = {};
                        attr.ProductAttributeValues.forEach((attrValue: any) => {
                            optionsToSet[attrValue.Id] = false;
                        });

                        inputsToSetUpdated = true;
                        inputsToSet.push({id: attr.Id, value: optionsToSet, userFields: tempUserFields});
                    } else {
                        let splittedValue: string[] = valueToSet.split(', ');
                        let optionsToSet: any = {};

                        let optionsAlreadySetToTrue: string[] = [];
                        splittedValue.forEach((value: string) => {
                            attr.ProductAttributeValues.forEach((attrValue: any) => {
                                if(value === attrValue.Name) {
                                    optionsToSet[attrValue.Id] = true;
                                    optionsAlreadySetToTrue.push(attrValue.Id);
                                } else if(!optionsAlreadySetToTrue.includes(attrValue.Id)) {
                                    optionsToSet[attrValue.Id] = false;
                                }
                            });
                        });

                        if(Object.keys(optionsToSet).length > 0) {
                            valueToSet = optionsToSet;
                            inputsToSetUpdated = true;
                            inputsToSet.push({id: attr.Id, value: valueToSet, userFields: tempUserFields});
                        }
                    }
                } else if(property.type === Controls.Picker) {
                    const filteredAttrValue: any = filterBySome(attr.ProductAttributeValues, 'Name', valueToSet);   

                    if(filteredAttrValue && filteredAttrValue.Id && inputsTypeValuePairs[attr.Id] !== filteredAttrValue.Id &&
                        (!this.conditionalDefaultValueAlreadySetFor[attr.Id] || this.conditionalDefaultValueAlreadySetFor[attr.Id].settedValue !== filteredAttrValue.Id)
                    ) { 
                        valueToSet = filteredAttrValue.Id;
                        inputsToSetUpdated = true;
                        inputsToSet.push({id: attr.Id, value: valueToSet, userFields: tempUserFields});
                    }
                } else if(inputsTypeValuePairs[attr.Id] !== valueToSet) {
                    inputsToSetUpdated = true;
                    inputsToSet.push({id: attr.Id, value: valueToSet, userFields: tempUserFields});
                }

                this.lastSetDefaultValueTimestamp = + new Date();

                if(inputsToSetUpdated === true) {
                    this.conditionalDefaultValueAlreadySetFor[attr.Id] = {
                        value: conditionalAttributeResult.newValue,
                        settedValue: valueToSet
                    };

                    shouldMassUpdateInputs = true;
                }
            }

            if(!!tempUserFields && !this.props.inputsIdUserFieldsPairs[attr.Id]) {
                this.props.setInputsUserFields(property.id, tempUserFields);
            }

            if(conditionalAttributeResult.show === true) {
                if(!inputsTypeValuePairs[attr.Id] || inputsTypeValuePairs[attr.Id] === '') {
                    inputsTypeValuePairs[attr.Id] = this.props.hiddenInputsTypeValuePairs[attr.Id];
                    this.props.setHiddenInputsTypeValuePairs(attr.Id, undefined); 
                }

                if(conditionalAttributeResult.disabled === true) {
                    property.disabled = true;
                }

                let errorMessage: any = !!this.props.inputErrorsText[property.id] ? this.props.inputErrorsText : undefined;
                if(typeof errorMessage !== 'undefined') {
                    tabHasError = true;
                }

                if(tempUserFields === null || !isInTab) {
                    return renderElement(property, conditionalAttributeResult.show, iconData, this, errorMessage);
                } else {
                    this.parseTabs(tempUserFields, renderElement(property, conditionalAttributeResult.show, iconData, this, errorMessage), attr.Id, attr.ProductAttributeId, this.tabsParsed, tabHasError);
                    return false;
                }
            } else {
                if(this.tempInputErrors > this.prevTempInputErrors) {
                    this.tempInputErrors--;
                }

                if(!!tempUserFields) {
                    if(isInTab) {
                        this.deleteFromTab(attr.ProductAttributeId);
                    }
                    const isHideUserField: any = tempUserFields.filter((x: any) => x.Key === 'Hide');
                    if(!isHideUserField || !isHideUserField[0] || !isHideUserField[0].Value || isHideUserField[0].Value !== 'true') {
                        if(!!inputsTypeValuePairs[attr.Id]) {
                            this.props.setHiddenInputsTypeValuePairs(attr.Id, inputsTypeValuePairs[attr.Id]);
                        }
                        delete inputsTypeValuePairs[attr.Id];
                    }
                } else {
                    delete inputsTypeValuePairs[attr.Id];
                }
                delete this.conditionalDefaultValueAlreadySetFor[attr.Id];
                return false;
            }
        }) : []);

        this.savedAttributes = attributes;

        if(shouldMassUpdateInputs !== false) {
            this.setConditionalDefaultInputsData(inputsToSet);
        }

        return attributes;
    }

    handleTabSwitch = (tabKey?: string) => {
        const tempCurrentSelectedKey: number = parseInt(this.tabsSelectedKey);
        this.tabsSelectedKey = (typeof tabKey === 'string' && !!tabKey) ? tabKey : (tempCurrentSelectedKey === (this.tabs.length - 1) ? '0' : (tempCurrentSelectedKey + 1).toString());

        if (this.props.onTabChange) {
            this.props.onTabChange(this.tabsSelectedKey);
        }
        this.forceUpdate();
    }

    loopTabs = () => {
        this.props.changeLoadSpinnerLabel('Filling in field values ​​based on the edited calculation...');
        this.props.toggleAsyncActionFlag(true, false);

        const waitBetweenTabSwitchTime: number = 500; //ms

        for(let i = 0; i < this.tabs.length; i++) {
            setTimeout(() => {
                this.tabsSelectedKey = `${i}`;
                this.forceUpdate();
            }, (i + 1) * waitBetweenTabSwitchTime);
        }

        setTimeout(() => {
            this.tabsSelectedKey = '0';
            this.props.changeLoadSpinnerLabel(null);
            this.props.toggleAsyncActionFlag(false, false);
            this.forceUpdate();
        }, (this.tabs.length + 1) * waitBetweenTabSwitchTime);
    }

    render() {
        const {productAttributes, product, inputValuePairsStringified, customInputsData, inputErrorsText} = this.props;
        
        if(product && product.UserFields && this.tabs.length === 0 && !this.tabsParsed) {
            this.setTabs(product.UserFields);
        }
        
        let attributes: any;
        if(Object.keys(this.savedAttributes).length === 0 || this.prevInputValuePairsStringified !== inputValuePairsStringified || 
            this.prevInputErrorsTextStringified !== JSON.stringify(inputErrorsText) || JSON.stringify(customInputsData) !== this.prevCustomInputsDataStringified ||
            this.prevAsyncActionInprogress !== this.props.asyncActionInProgress
        ) {
            this.tabs.forEach((tab: any, tabIndex: number) => {
                this.tabs[tabIndex].tabErrorsCount = 0;
            });
            
            attributes = this.mapAttributes();
        } else {
            attributes = this.savedAttributes;
        }

        this.prevInputValuePairsStringified = inputValuePairsStringified;
        this.prevInputErrorsTextStringified = JSON.stringify(inputErrorsText);
        this.prevCustomInputsDataStringified = JSON.stringify(customInputsData);
        this.prevAsyncActionInprogress = this.props.asyncActionInProgress;

        attributes = attributes.filter((x: any) => x !== false);

        if(productAttributes && !this.tabsParsed) {
            this.tabsParsed = true;
        }
        
        if(this.props.inputErrors !== this.tempInputErrors) {
            this.props.setInputErrors(this.tempInputErrors);
        }

        let inputsToMassChange: any[] = [];
        let tabs = this.tabs.map((tab: any, tabIndex: number) => {
            tab.items = tab.items.slice(0).sort(this.sortByOrder);

            let tabItems: any[] = [];
            tab.items.forEach((item: any) => {
                if(!item.hide) {
                    tabItems.push(<div className={classNames.myTabItem} key={item.key}>{item.value}</div>);
                } else if(this.props.isEditMode && this.tabInputsInEditModeSet === false) {
                    inputsToMassChange.push(item);
                }
            });

            return <PivotItem headerText={tab.name} key={`${tab.name}-${tabIndex.toString()}`} itemKey={tabIndex.toString()} itemIcon={tab.tabErrorsCount && tab.tabErrorsCount > 0 ? 'Error' : undefined}
                        itemCount={tab.tabErrorsCount && tab.tabErrorsCount > 0 ? tab.tabErrorsCount : undefined} className={classNames.pivotItemsContainer}
            >
                <FocusZone direction={FocusZoneDirection.horizontal} handleTabKey={FocusZoneTabbableElements.all} isCircularNavigation={true}>
                    {(product && product.SeName === 'ubezpieczenie-auta' && tabIndex === AppConfig.tabNumberForCustomInputsInMotorInsurance - 1) &&
                        <>
                            {Object.keys(customInputsData).length > 0 &&
                                <Card className={classNames.vehicleConfigurationCard}>
                                    <CardHeader
                                        header={L('Selected vehicle configuration')}
                                    />

                                    <ul>
                                        {Object.keys(customInputsData).map((key: string, index: number) => (
                                            <li key={index}>
                                                <span style={{fontWeight: 'bold'}}>{L(key)}:</span><br />{L(customInputsData[key])}
                                            </li>
                                        ))}
                                    </ul>
                                </Card>
                            }

                            <EurotaxExpertInfoMixedCustomInputsBox showCustomInputsBox={this.showEurotaxExpertInfoMixedCustomInputsBox} customInputsData={customInputsData}
                                onInputChange={ (id: string, value: any) => this.props.onInputChange(id, value, this.tempInputIdUserFieldPairs[id]) }
                                mapKeyToId={(mapType: string, key: string) => this.props.mapKeyToId(mapType, key)}
                                onToggleClose={() => { this.showEurotaxExpertInfoMixedCustomInputsBox = false; this.forceUpdate(); }}
                                setEurotaxInfoexpertFormData={ (id: string, value: string) => this.props.setEurotaxInfoexpertFormData(id, value) }
                                toggleAsyncActionFlag={this.props.toggleAsyncActionFlag}
                            />
                        </>
                    }
                    {tabItems}
                </FocusZone>
            </PivotItem>;
        });

        if(this.tabInputsInEditModeSet === false) {
            this.tabInputsInEditModeSet = true;
            
            const cloneInputsTypeValuePairs = {};
            const cloneInputsIdUserFieldsPairs = {};

            inputsToMassChange.forEach((element) => {
                if(!!element.value.props.value) {
                    cloneInputsTypeValuePairs[element.value.key] = !!element.value.props.value ? 
                                                            (isJsonString(element.value.props.value) ? 
                                                                JSON.parse(element.value.props.value) : element.value.props.value) 
                                                            : '';
                    cloneInputsIdUserFieldsPairs[element.value.key] = this.tempInputIdUserFieldPairs[element.value.key];
                }
            });
            
            this.props.onMassInputChange(cloneInputsTypeValuePairs, cloneInputsIdUserFieldsPairs);

            if(this.props.isEditMode === true) {
                this.loopTabs();
            }
        }

        this.lastRenderTimestamp = + new Date();

        const pivotStyles = {
            root: {
                marginLeft: '-8px'
            },
            linkIsSelected: {
                color: myTheme.palette.red,
                selectors: {
                    ':before': {
                        height: '5px',
                        backgroundColor: additionalTheme.darkerRed,
                    }
                }
            }
        };

        return <>
            <Modal
                titleAriaId={'selectVehicleModal'}
                isOpen={this.isVehicleListModalOpen}
                onDismiss={() => { this.isVehicleListModalOpen = false; this.forceUpdate(); }}
                isBlocking={true}
                containerClassName={classNames.container}
            >
                <div className={classNames.header}>
                    {/* <span id={'selectCustomerModal'}>{L("Select customer:")}</span> */}
                    <IconButton
                        styles={iconButtonStyles}
                        iconProps={cancelIcon}
                        ariaLabel={L("Close popup modal")}
                        onClick={() => { this.isVehicleListModalOpen = false; this.props.onTempVehicleSelect(undefined, false); }}
                    />
                </div>
            

                <div className={classNames.contentContainer}>
                    <VehicleListBase selectedVehicle={this.props.tempSelectedVehicle} vehicleStore={this.props.vehicleStore} alignItemsToRight={true}
                        onVehicleSelect={ (tempVehicle: any) => {
                            this.props.onTempVehicleSelect(tempVehicle, false); 
                        }}
                        showCounter={false} showFilters={false} showFillDataButton={true}
                        onFillDataButtonClick={(selectedVehicle: any) => {
                            let getIdFromInputKeys: any = {
                                "vehicleType": this.props.mapKeyToId("mapAttributeNameToId", "vehicleType"),
                                "productionYear": this.props.mapKeyToId("mapAttributeNameToId", "productionYear"),
                                "vin": this.props.mapKeyToId("mapAttributeNameToId", "vin"),
                                "registrationNumber": this.props.mapKeyToId("mapAttributeNameToId", "registrationNumber"),
                                "firstRegistrationDate": this.props.mapKeyToId("mapAttributeNameToId", "firstRegistrationDate"),
                                "fuelType": this.props.mapKeyToId("mapAttributeNameToId", "fuelType"),
                                "eurotaxId": this.props.mapKeyToId("mapAttributeNameToId", "eurotaxId"),
                                "expertInfoId": this.props.mapKeyToId("mapAttributeNameToId", "expertInfoId"),
                                "findAVehicle": this.props.mapKeyToId("mapAttributeNameToId", "findAVehicle"),
                                "mileage": this.props.mapKeyToId("mapAttributeNameToId", "mileage"),
                                "dmc": this.props.mapKeyToId("mapAttributeNameToId", "dmc"),
                                "netWeight": this.props.mapKeyToId("mapAttributeNameToId", "netWeight"),
                                "capacity": this.props.mapKeyToId("mapAttributeNameToId", "capacity"),
                            };

                            let eurotaxSet: boolean = selectedVehicle.eurotaxCarId && selectedVehicle.eurotaxCarId.length > 0;
                            let infoExpertSet: boolean = selectedVehicle.infoExpertId && selectedVehicle.infoExpertId.length > 0;

                            const cloneInputsTypeValuePairs = {
                                [getIdFromInputKeys["productionYear"]]: selectedVehicle.productionYear,
                                [getIdFromInputKeys["vin"]]: selectedVehicle.vin,
                                [getIdFromInputKeys["registrationNumber"]]: selectedVehicle.registrationNumber,
                                [getIdFromInputKeys["firstRegistrationDate"]]: selectedVehicle.firstRegistrationDate,
                                [getIdFromInputKeys["fuelType"]]: selectedVehicle.fuelType,
                                [getIdFromInputKeys["eurotaxId"]]: selectedVehicle.eurotaxCarId && selectedVehicle.eurotaxCarId.toString(),
                                [getIdFromInputKeys["expertInfoId"]]: selectedVehicle.infoExpertId && selectedVehicle.infoExpertId.toString(),
                                [getIdFromInputKeys["mileage"]]: selectedVehicle.mileage,
                                [getIdFromInputKeys["vehicleType"]]: selectedVehicle.vehicleType,
                                [getIdFromInputKeys["findAVehicle"]]: (eurotaxSet && infoExpertSet) ?
                                                                            L("Eurotax ID and Expert's information ID is now set.") :
                                                                                (eurotaxSet ? L('Success! Eurotax ID is now set.') :
                                                                                    (infoExpertSet ? L("Success! Expert's information ID is now set.") : ""))
                            };
                            const cloneInputsIdUserFieldsPairs = {
                                [getIdFromInputKeys["productionYear"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["productionYear"]],
                                [getIdFromInputKeys["vin"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["vin"]],
                                [getIdFromInputKeys["registrationNumber"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["registrationNumber"]],
                                [getIdFromInputKeys["firstRegistrationDate"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["firstRegistrationDate"]],
                                [getIdFromInputKeys["fuelType"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["fuelType"]],
                                [getIdFromInputKeys["eurotaxId"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["eurotaxId"]],
                                [getIdFromInputKeys["expertInfoId"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["expertInfoId"]],
                                [getIdFromInputKeys["mileage"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["mileage"]],
                                [getIdFromInputKeys["vehicleType"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["vehicleType"]],
                                [getIdFromInputKeys["findAVehicle"]]: this.tempInputIdUserFieldPairs[getIdFromInputKeys["findAVehicle"]],
                            };

                            this.isVehicleListModalOpen = false;
                            this.props.onMassInputChange(cloneInputsTypeValuePairs, cloneInputsIdUserFieldsPairs);
                        }}
                    />
                </div>
            </Modal>

            {/* <div style={{display: 'flex', flexDirection: 'row', 'alignItems': 'center'}}>
                {!isConfigForProduction() &&
                    <DefaultButton text={L('Fill with example data')} allowDisabledFocus style={{margin: '15px 25px 15px 0'}} disabled={this.fillingExampleDataActionInProgress}
                        onClick={() => { this.fillWithExampleData(); }}
                    />
                }
            
                <Slider label={L('Fast calculation')} min={0} max={1} value={this.props.isFastCalculation ? 1 : 0} showValue={false}
                    className={`${classNames.toggleSlider} ${this.props.isFastCalculation === true && classNames.toggleSliderActive}`} onChange={(value: number) => {
                        this.props.setIsFastCalculation(value === 1 ? true : false);
                    }}
                />

                {this.fillingExampleDataActionInProgress ? <Spinner label={L('Filling in data...')} className={classNames.loadSpinnerTopBar} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" /> : ''}
            </div> */}

            <Pivot className={`${this.tabs.length > 0 ? '' : classNames.toolbar} ${classNames.pivotItemButton}`} theme={myTheme} selectedKey={this.tabsSelectedKey} styles={pivotStyles}
                onLinkClick={(item?: PivotItem) => {
                    if (item && item.props) {
                        this.handleTabSwitch(item.props.itemKey);
                        this.forceUpdate();
                    }
                }
            }>
                {this.tabs.length > 0 ?
                    tabs :
                    <PivotItem>
                        {attributes}
                    </PivotItem>
                }
            </Pivot>
        </>;
    }

    private getMemoizedConditionalAttribute(attr: any, inputsTypeValuePairs: any, idUserFieldsPairs: any, mappings: any, items: any, language: any): any {
        const attrId: string = attr.Id;
        
        // Determine which inputs affect this attribute
        if (!this.inputDependencies.has(attrId)) {
            const analyzeResult: {dependenciesIds: Set<string>, tempKeyIdPairs: Map<string, string>} = analyzeAttributeDependencies(
                attrId, attr, this.keyIdPairs, this.tempInputIdUserFieldPairs[attrId], mappings, this.props.product
            );

            this.inputDependencies.set(attrId, analyzeResult.dependenciesIds);
            this.keyIdPairs = new Map(analyzeResult.tempKeyIdPairs);
        }
        
        // Get the relevant inputs for this attribute
        const relevantInputs = this.inputDependencies.get(attrId) || new Set(Object.keys(inputsTypeValuePairs));
        
        // Create a dependency key from only the relevant inputs
        const inputsStateKey: string = createInputsStateKey(Array.from(relevantInputs), inputsTypeValuePairs);
        
        // Check cache
        const cacheKey: string = `${attrId}_${inputsStateKey}`;
        if (this.conditionalAttributeCache.has(cacheKey)) {
            return this.conditionalAttributeCache.get(cacheKey);
        }
        
        // Calculate new result
        const result = conditionalAttribute(attr, inputsTypeValuePairs, idUserFieldsPairs, mappings, items, language);
        
        // Cache the result
        this.conditionalAttributeCache.set(cacheKey, result);
        
        return result;
    }
}