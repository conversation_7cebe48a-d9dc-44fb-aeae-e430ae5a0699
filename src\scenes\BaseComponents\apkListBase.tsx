import * as React from 'react';
import { IColumn, ITextFieldProps, mergeStyleSets, ShimmeredDetailsList, Selection, SelectionMode, FontWeights, PrimaryButton, Icon, Spinner, SpinnerSize, ConstrainMode } from '@fluentui/react';
import { inject } from 'mobx-react';
import Stores from '../../stores/storeIdentifier';
import { L } from '../../lib/abpUtility';
import { LabelComponent } from './labelComponent';
import {additionalTheme, myTheme} from '../../styles/theme';
import { ITableColumn } from './ITableColumn';
import { utilMapToColumn } from '../../utils/tableUtils';
import { LabelContainerComponent } from './labelContainerComponent';
import { dateFormat } from '../../utils/utils';
import { spinnerClassNames } from '../../styles/spinnerStyles';

const classNames = mergeStyleSets({
  hide: {
    display: 'none',
  },
  apkListWrapper: {
    maxHeight: '350px',
    maxWidth: '100%',
    width: '100%',
    overflow: 'auto',
    margin: '15px 0 0',
    selectors: {
      // '.ms-DetailsHeader-cell': {
      //   minWidth: '150px',
      // },
      // '.ms-DetailsRow-cell': {
      //   minWidth: '150px',
      // },
      // '.ms-DetailsRow-cellCheck': {
      //   minWidth: 'auto',
      //   width: 'auto',
      // }
    }
  },
  apkList: {
    selectors: {
      '& .ms-DetailsHeader': {
          paddingTop: '0',
      }
    }
  },
  labelComponentCustom: {
  },
  filterDropdownWrapper: {
    marginLeft: '25px',
    selectors: {
      '& > div': {
        margin: '0 !important'
      }
    }
  },
  tableItemsCounter: {
    margin: "5px 0 0 25px",
    fontWeight: FontWeights.bold,
  },
});

export interface IApkListBaseProps extends ITextFieldProps {
  selectedApk: string;
  apkAttachedFilesStore: any;
  filterByKey?: string;
  filterValue?: string;
  hideList?: boolean;
  customStyle?: any;
  showFilters?: boolean;
  showCounter?: boolean;
  showFillDataButton?: boolean;
  alignItemsToRight?: boolean;
  asyncActionInProgress?: boolean;
  onApkSelect: (apk: any) => void;
  onFillDataButtonClick: (selectedApk: any) => void;
  toggleAsyncActionFlag?: (newState: boolean, forceUpdate: boolean) => void;
}

type IApkListBaseState = { 
  columnsState: any,
  tableItems: any[],
  allTableItems: any[],
  tableItemsSet: boolean,
  filter: string,
};

@inject(Stores.EventStore)
export class ApkListBase extends React.Component<IApkListBaseProps, IApkListBaseState> {
  private defaultColumns: IColumn[] = [];
  private apkPreselected: boolean = false;
  private initSelection: boolean = true;
  private _selection: Selection;

  constructor(props: IApkListBaseProps) {
    super(props);

    this._selection = new Selection({
      onSelectionChanged: () => {
        if(!this.initSelection) {
            this.props.onApkSelect(this._selection.getSelection()[0]);
        } else {
          this.initSelection = false;
          this.forceUpdate();
        }
      },
    });

    this.state = {
      ...this.state,
      columnsState: {},
      tableItems: [],
      allTableItems: [],
      filter: "",
      tableItemsSet: false,
    };
  }

  componentDidMount() {
    const mappedColumns = utilMapToColumn(this.getTableColumns());
    let newColumnState = {...this.state.columnsState};

    mappedColumns.forEach((col) => {
      if(!this.state.columnsState[col.key]) {
        newColumnState[col.key] = {
            "isSortedDescending": true
        };
      }
    });

    let tableItems = this.props.apkAttachedFilesStore.dataSet && this.props.apkAttachedFilesStore.dataSet.totalCount > 0 ? this.props.apkAttachedFilesStore.dataSet.items : [];

    this.setState({ columnsState: newColumnState, tableItems: tableItems, allTableItems: tableItems });
  }

  componentDidUpdate() {
    if(!this.initSelection && !this.apkPreselected && this.props.selectedApk && this._selection.getSelectedIndices().length === 0 &&
        (this.props.apkAttachedFilesStore.dataSet && this.props.apkAttachedFilesStore.dataSet.totalCount > 0) && this.state.tableItems.length > 0) {
      
      const foundIndex = this.getIndexByItemId(this.props.selectedApk);

      if(foundIndex >= 0) {
        this._selection.setIndexSelected(foundIndex, true, false);
        this.apkPreselected = true;
        this.forceUpdate();
      }
    }
  }

  private getIndexByItemId(itemId: string): number {
    let indexToReturn = -1;
    
    if(this.state.tableItems.length > 0) {
        this.state.tableItems.some((item: any, index: number) => {
          if(item.id === itemId) {
              indexToReturn = index;
              return true;
          }
          return false;
        })
    }

    return indexToReturn;
  }

  private getTableColumns(): ITableColumn[] {
    return [
      {
        name: L('APK number'),
        fieldName: 'displayedFileName',
        minWidth: 180,
        maxWidth: 180,
      },
      // {
      //   name: L('Client name'),
      //   fieldName: 'client.user.fullName',
      //   minWidth: 250,
      //   maxWidth: 250,
      //   onRender: (item: any): any => {
      //     return item && item.client && item.client.user ? item.client.user.fullName : (item && item.client && !!item.client.company ? item.client.company : "");
      //   }
      // },
      {
        name: L('Company name'),
        fieldName: 'client.company',
        minWidth: 250,
        maxWidth: 250,
        onRender: (item: any): any => {
          return item && item.client ? item.client.company : "";
        }
      },
      {
        name: L('Client name'),
        fieldName: 'client.user.fullName',
        onRender: (item: any): any => {
          return item && item.client && item.client.user ? item.client.user.fullName : "";
        }
      },
      {
        name: L('Product'),
        fieldName: 'productName',
        minWidth: 100,
        maxWidth: 100,
        onRender: (item: any): any => {
          return L(`${item.productName}2`);
        }
      },
      {
        name: L('Status'),
        fieldName: 'status',
        minWidth: 80,
        maxWidth: 80,
        onRender: (item: any): any => {
          let textColor = additionalTheme.white;
          let background = myTheme.palette.orange;

          if (item.status === 'Prepared') {
            textColor = additionalTheme.white;
            background = myTheme.palette.green;
          }  if (item.status === 'Expired') {
            textColor = additionalTheme.white;
            background = additionalTheme.lighterRed;
          } if (item.status === 'Sended') {
            textColor = additionalTheme.white;
            background = myTheme.palette.green;
          } if (item.status === 'Signed') {
            textColor = additionalTheme.white;
            background = myTheme.palette.green;
          }
          return (
              <span style={{ color:textColor, backgroundColor: background, padding: '2px 5px', borderRadius: '2px' }}>
            {L(item.status)}
          </span>
          );
        }
      },
      {
        minWidth: 95,
        maxWidth: 95,
        name: L('Policy issued?'),
        fieldName: 'policyIssued',
        onRender: (item: any) => {
          return <Icon style={{ color: !!item.policyIssued && item.policyIssued === true ? "green" : "red", margin: '0 auto', display: 'block', width: 'min-content'}} 
                      iconName={ !!item.policyIssued && item.policyIssued === true ? "SkypeCheck" : "StatusCircleErrorX" } />
        }
      },
      {
        name: L('Created by'),
        fieldName: 'creationWay',
        minWidth: 80,
        maxWidth: 80,
        onRender: (item: any): any => {
          return <span style={{ color: additionalTheme.white, backgroundColor: myTheme.palette.orange, padding: '2px 5px', borderRadius: '2px' }}>
            {L(item.creationWay)}
          </span>;
        }
      },
      {
        name: L('Creation time'),
        fieldName: 'creationTime',
        minWidth: 130,
        maxWidth: 130,
        onRender: (item: any): any => {
          return dateFormat(item.creationTime, undefined, true);
        }
      },
    ];
  }

  private mapToColumn(tableColumns: ITableColumn[]) {
    return utilMapToColumn(tableColumns)
  }

  private sortApk(column: IColumn | undefined): void {
    if(column && column.name) {
      let tempTableItems = [...this.state.tableItems];

      if(this.state.columnsState[column.key].isSortedDescending) {
        tempTableItems = tempTableItems.slice(0).sort((a, b) => (a[column.name] < b[column.name]) ? 1 : ((b[column.name] < a[column.name]) ? -1 : 0));
      } else {
        tempTableItems = tempTableItems.slice(0).sort((a, b) => (a[column.name] > b[column.name]) ? 1 : ((b[column.name] > a[column.name]) ? -1 : 0));
      }
      
      let newColumnState = {...this.state.columnsState};
      newColumnState[column.key].isSortedDescending = !newColumnState[column.key].isSortedDescending;
      this.setState({ columnsState: newColumnState, tableItems: tempTableItems });
    }
  }

  private onColumnClick = (ev?: React.MouseEvent<HTMLElement, MouseEvent> | undefined, newCol?: IColumn | undefined) => {
    this._selection.setAllSelected(false);
    this.props.onApkSelect('');
    this.sortApk(newCol);
  }

  private setTable() {
    let tableColumns = this.getTableColumns();
    this.defaultColumns = this.mapToColumn(tableColumns);

    if(this.state.tableItems.length === 0) {
      let tableItems = this.props.apkAttachedFilesStore.dataSet && this.props.apkAttachedFilesStore.dataSet.totalCount > 0 ? this.props.apkAttachedFilesStore.dataSet.items : [];

      let tableItemsSet: boolean = true;
      // if(tableItems.length > 0) {
        // tableItemsSet = true;
      // }

      this.setState({ tableItems: tableItems, allTableItems: tableItems, tableItemsSet });
    }
  }

  render() {
    const { apkAttachedFilesStore, customStyle, showFilters, hideList, alignItemsToRight, asyncActionInProgress } = this.props;
    const { tableItems, tableItemsSet } = this.state;

    if(!tableItemsSet && ((tableItems.length === 0 && apkAttachedFilesStore.dataSet && apkAttachedFilesStore.dataSet.totalCount > 0) || this.defaultColumns.length === 0)) {
      this.setTable();
    }

    return <div className={`${hideList === true && classNames.hide}`} style={customStyle && customStyle}>
        <LabelContainerComponent alignItemsToRight={alignItemsToRight}>
          {asyncActionInProgress &&
            <Spinner label={L('Please wait...')} className={spinnerClassNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="left"
              style={{marginRight: 20, marginTop: 3}} />
          }

          <LabelComponent label={L('Select APK')} className={classNames.labelComponentCustom} customStyles={showFilters && {width: '200px'}} />
          {this.props.showFillDataButton && 
            <PrimaryButton style={{marginLeft: 20}} text={L("Fill in the form with the data of the selected APK")}
              disabled={this._selection.getSelection().length === 0 || asyncActionInProgress}
              onClick={() => {
                if(this.props.toggleAsyncActionFlag) {
                  this.props.toggleAsyncActionFlag(true, true);
                }
                this.props.onFillDataButtonClick(this._selection.getSelection()[0]);
              }}
            />
          }
        </LabelContainerComponent>

        <div className={classNames.apkListWrapper}>
          <ShimmeredDetailsList
            constrainMode={ConstrainMode.unconstrained}
            columns={this.defaultColumns}
            items={tableItems}
            selectionMode={SelectionMode.single}
            selection={this._selection}
            className={classNames.apkList}
            onColumnHeaderClick={this.onColumnClick}
            enableShimmer={!tableItemsSet && tableItems.length === 0}
            selectionPreservedOnEmptyClick={true}
          />
        </div>
    </div>;
  }
}