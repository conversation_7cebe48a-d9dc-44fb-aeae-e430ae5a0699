import * as React from 'react';
import { IColumn, ITextFieldProps, mergeStyleSets, ShimmeredDetailsList, Selection, SelectionMode, SearchBox, IDropdownOption, FontWeights, PrimaryButton, ConstrainMode } from '@fluentui/react';
import { inject } from 'mobx-react';
import Stores from '../../stores/storeIdentifier';
import { L } from '../../lib/abpUtility';
import { LabelComponent } from './labelComponent';
import { myTheme } from '../../styles/theme';
import { ITableColumn } from './ITableColumn';
import { utilMapToColumn } from '../../utils/tableUtils';
import { LabelContainerComponent } from './labelContainerComponent';

const classNames = mergeStyleSets({
  hide: {
    display: 'none',
  },
  vehicleListWrapper: {
    maxHeight: '350px',
    maxWidth: '100%',
    width: '100%',
    overflow: 'auto',
    margin: '15px 0 0',
    border: `1px solid ${myTheme.palette.themeLighter}`,
    selectors: {
      // '.ms-DetailsHeader-cell': {
      //   minWidth: '150px',
      // },
      // '.ms-DetailsRow-cell': {
      //   minWidth: '150px',
      // },
      // '.ms-DetailsRow-cellCheck': {
      //   minWidth: 'auto',
      //   width: 'auto',
      // }
    }
  },
  vehicleList: {
    selectors: {
      '& .ms-DetailsHeader': {
          paddingTop: '0',
      }
    }
  },
  labelComponentCustom: {
  },
  filterDropdownWrapper: {
    marginLeft: '25px',
    selectors: {
      '& > div': {
        margin: '0 !important'
      }
    }
  },
  tableItemsCounter: {
    margin: "5px 0 0 25px",
    fontWeight: FontWeights.bold,
  },
});

export interface IVehicleListBaseProps extends ITextFieldProps {
  selectedVehicle: string;
  vehicleStore: any;
  filterByKey?: string;
  filterValue?: string;
  hideList?: boolean;
  customStyle?: any;
  showFilters?: boolean;
  showCounter?: boolean;
  showFillDataButton?: boolean;
  alignItemsToRight?: boolean;
  onVehicleSelect: (vehicle: any) => void;
  onFillDataButtonClick: (selectedVehicle: any) => void;
}

type IVehicleListBaseState = { 
  columnsState: any,
  tableItems: any[],
  allTableItems: any[],
  tableItemsSet: boolean,
  filter: string,
  filterByVehicleType: string,
  filterByVehicleTypeDropdownOptions: IDropdownOption[],
};

@inject(Stores.EventStore)
export class VehicleListBase extends React.Component<IVehicleListBaseProps, IVehicleListBaseState> {
  private defaultColumns: IColumn[] = [];
  private vehiclePreselected: boolean = false;
  private initSelection: boolean = true;
  private _selection: Selection;

  constructor(props: IVehicleListBaseProps) {
    super(props);

    this._selection = new Selection({
      onSelectionChanged: () => {
        if(!this.initSelection) {
            this.props.onVehicleSelect(this._selection.getSelection()[0]);
        } else {
          this.initSelection = false;
          this.forceUpdate();
        }
      },
    });

    this.state = {
      ...this.state,
      columnsState: {},
      tableItems: [],
      allTableItems: [],
      filter: "",
      filterByVehicleType: "all",
      filterByVehicleTypeDropdownOptions: [],
      tableItemsSet: false,
    };
  }

  componentDidMount() {
    const mappedColumns = utilMapToColumn(this.getTableColumns());
    let newColumnState = {...this.state.columnsState};

    mappedColumns.forEach((col) => {
      if(!this.state.columnsState[col.key]) {
        newColumnState[col.key] = {
            "isSortedDescending": true
        };
      }
    });

    let tableItems = this.props.vehicleStore.dataSet && this.props.vehicleStore.dataSet.totalCount > 0 ? this.props.vehicleStore.dataSet.items : [];
    // tableItems = tableItems.filter(this.filterVehicles, this);

    this.setState({ columnsState: newColumnState, tableItems: tableItems, allTableItems: tableItems });
  }

  componentDidUpdate() {
    if(!this.initSelection && !this.vehiclePreselected && this.props.selectedVehicle && this._selection.getSelectedIndices().length === 0 &&
        (this.props.vehicleStore.dataSet && this.props.vehicleStore.dataSet.totalCount > 0) && this.state.tableItems.length > 0) {
      
      const foundIndex = this.getIndexByItemId(this.props.selectedVehicle);

      if(foundIndex >= 0) {
        this._selection.setIndexSelected(foundIndex, true, false);
        this.vehiclePreselected = true;
        this.forceUpdate();
      }
    }
  }

  private getIndexByItemId(itemId: string): number {
    let indexToReturn = -1;
    
    if(this.state.tableItems.length > 0) {
        this.state.tableItems.some((item: any, index: number) => {
          if(item.id === itemId) {
              indexToReturn = index;
              return true;
          }
          return false;
        })
    }

    return indexToReturn;
  }

  private getTableColumns(): ITableColumn[] {
    return [
      // {
      //   name: L('ID'),
      //   fieldName: 'id',
      //   minWidth: 50,
      //   maxWidth: 50,
      //   onRender: (item: any): any => {
      //     return item.id
      //   }
      // },
      // {
      //   name: L('Owner'),
      //   fieldName: 'clientName',
      //   minWidth: 170,
      //   maxWidth: 170,
      // },
      {
        name: L('Registration number'),
        fieldName: 'registrationNumber',
        minWidth: 140,
        maxWidth: 140,
      },
      {
        name: L('Vehicle type'),
        fieldName: 'type',
        onRender: (item: any): any => {
          return L(`${item.type}`);
        }
      },
      {
        name: L('Production year'),
        fieldName: 'productionYear',
        minWidth: 120,
        maxWidth: 120,
      },
      {
        name: L('Fuel type'),
        fieldName: 'fuelType',
        onRender: (item: any): any => {
          return L(`${item.fuelType}`);
        }
      },
      {
        name: L('Mileage'),
        fieldName: 'mileage',
        minWidth: 70,
        maxWidth: 70,
      },
      {
        name: L('Info expert ID'),
        fieldName: 'infoExpertId',
        minWidth: 120,
        maxWidth: 120,
      },
      {
        name: L('Eurotax car ID'),
        fieldName: 'eurotaxCarId',
        minWidth: 120,
        maxWidth: 120,
      },
      {
        name: L('Vehicle info'),
        fieldName: 'vehicleInfo',
        minWidth: 120,
        maxWidth: 120,
      },
    ];
  }

  private mapToColumn(tableColumns: ITableColumn[]) {
    return utilMapToColumn(tableColumns)
  }

  private getItemFilterString(item: any): string {
    let array: string[] = [];
    this.getTableColumns().forEach((x) => {
      let fieldName = x.fieldName;
      let splittedFieldName: string[] = fieldName.split('.');

      if(splittedFieldName.length > 1) {
        let deeperModelValue = [];
        deeperModelValue[0] = item;
        for(let i = 1; i <= splittedFieldName.length; i++) {
          deeperModelValue[i] = deeperModelValue[i-1][splittedFieldName[i-1]];
        }

        if(!!deeperModelValue[deeperModelValue.length - 1] && deeperModelValue[deeperModelValue.length - 1].length > 0) {
          array.push(deeperModelValue[deeperModelValue.length - 1]);
        }
      } else {
        let property = item[fieldName];
        if(!!property && property.length > 0) {
          array.push(property);
        }
      }
    });

    return array.join();
  }

  private filterVehicles(value: any) {
    const { filterByKey, filterValue } = this.props;

    if(!!filterByKey && !!filterValue) {
      if(Array.isArray(value[filterByKey])) {
        return value[filterByKey].includes(filterValue) && !value.Deleted;
      } else {
        return value[filterByKey] === filterValue && !value.Deleted;
      }
    }
    return !value.Deleted;
  }

  private filterByVehicleType(vehicleType: string) {
    this.setState((prevState) => {
      if(vehicleType !== prevState.filterByVehicleType) {
        let tempTableItems = [...this.state.allTableItems];
        if(vehicleType.length > 0) {
          tempTableItems = tempTableItems.filter((x) => x.vehicleType === vehicleType);
        }

        return ({...prevState, tableItems: tempTableItems, filterByVehicleType: vehicleType});
      } else {
        return ({...prevState});
      }
    });
  }

  private filterBySearch(newValue: string, forceSearch?: boolean, newVehicleType?: string) {
    this.setState((prevState) => {
      if(newValue !== prevState.filter || forceSearch === true) {
        let tempTableItems = [...this.state.allTableItems];
        if(newValue.length > 0) {
          newValue = newValue.split(" ").join(',');
          tempTableItems = tempTableItems.filter((i) => this.getItemFilterString(i).toString().toLowerCase().indexOf(newValue.toLowerCase()) > -1);
        }

        let finalTableItems: any[] = [];
        if(!!newVehicleType && newVehicleType !== 'all') {
          tempTableItems.forEach((tableItem: any) => {
            if(tableItem.vehicleType === newVehicleType) {
              finalTableItems.push(tableItem);
            }
          });
        } else {
          finalTableItems = tempTableItems;
        }

        return ({...prevState, tableItems: finalTableItems, filter: newValue});
      } else {
        return ({...prevState});
      }
    });
  }

  private sortVehicles(column: IColumn | undefined): void {
    if(column && column.name) {
      let tempTableItems = [...this.state.tableItems];

      if(this.state.columnsState[column.key].isSortedDescending) {
        tempTableItems = tempTableItems.slice(0).sort((a, b) => (a[column.name] < b[column.name]) ? 1 : ((b[column.name] < a[column.name]) ? -1 : 0));
      } else {
        tempTableItems = tempTableItems.slice(0).sort((a, b) => (a[column.name] > b[column.name]) ? 1 : ((b[column.name] > a[column.name]) ? -1 : 0));
      }
      
      let newColumnState = {...this.state.columnsState};
      newColumnState[column.key].isSortedDescending = !newColumnState[column.key].isSortedDescending;
      this.setState({ columnsState: newColumnState, tableItems: tempTableItems });
    }
  }

  private onColumnClick = (ev?: React.MouseEvent<HTMLElement, MouseEvent> | undefined, newCol?: IColumn | undefined) => {
    this._selection.setAllSelected(false);
    this.props.onVehicleSelect('');
    this.sortVehicles(newCol);
  }

  private setTable() {
    let tableColumns = this.getTableColumns();
    this.defaultColumns = this.mapToColumn(tableColumns);

    if(this.state.tableItems.length === 0) {
      let tableItems = this.props.vehicleStore.dataSet && this.props.vehicleStore.dataSet.totalCount > 0 ? this.props.vehicleStore.dataSet.items : [];
      // tableItems = tableItems.filter(this.filterVehicles, this);

      let tableItemsSet: boolean = true;
      // if(tableItems.length > 0) {
        // tableItemsSet = true;
      // }

      this.setState({ tableItems: tableItems, allTableItems: tableItems, tableItemsSet });
    }
  }

  private setDropdownOptions() {
    let tempOptions: IDropdownOption[] = [
      { key: 'all', text: L('All2') },
    ];

    if(tempOptions.length > this.state.filterByVehicleTypeDropdownOptions.length) {
      this.setState({ filterByVehicleTypeDropdownOptions: [...tempOptions] });
    }
  }

  render() {
    const { vehicleStore, customStyle, showFilters, hideList, alignItemsToRight } = this.props;
    const { filterByVehicleTypeDropdownOptions, tableItems, filterByVehicleType, tableItemsSet } = this.state;

    if(!tableItemsSet && ((tableItems.length === 0 && vehicleStore.dataSet && vehicleStore.dataSet.totalCount > 0) || this.defaultColumns.length === 0)) {
      if(!this.state.filterByVehicleType || this.state.filterByVehicleType.length === 0 || this.state.filterByVehicleType === 'all') {
        this.setTable();
      }
    }

    if(!filterByVehicleTypeDropdownOptions || filterByVehicleTypeDropdownOptions.length <= 1) {
      this.setDropdownOptions();
    }

    return <div className={`${hideList === true && classNames.hide}`} style={customStyle && customStyle}>
        <LabelContainerComponent alignItemsToRight={alignItemsToRight}>
          <LabelComponent label={L('Select vehicle')} className={classNames.labelComponentCustom} customStyles={showFilters && {width: '200px'}} />
          <SearchBox
            theme={myTheme}
            styles={{
              root: {
                flex: 1,
                maxWidth: '252px',
                height: '32px', 
                backgroundColor: myTheme.palette.white,
                border: `1px solid ${myTheme.palette.black}`,
                boxSizing: 'border-box',
              },
              field: { borderRadius: '2px' },
            }}
            placeholder={ L('Search') }
            onChange={ (e: any, newValue: string | undefined) => {
              newValue = typeof newValue === 'undefined' ? "" : newValue;
              this.filterBySearch(newValue, false, filterByVehicleType);
            }}
            // onSearch={ (newValue: any) => doSomething(newValue) }
          />

          {/* { showFilters &&
            <div className={classNames.filterDropdownWrapper}>
              <DropdownBase key={'filterByVehicleTypeDropdown'} required={false} label={L('Filter by client type')} options={filterByVehicleTypeDropdownOptions}
                value={filterByVehicleType} disabled={false} isDataLoaded={true} customDropdownWidth="250px" 
                customLabelStyles={{minWidth: '200px', width: '200px'}}
                onChange={(e: string | number | undefined) => {
                  if(e !== filterByVehicleType) {
                    this.filterByVehicleType((e && typeof e === 'string' && e.length > 0 ? e : "all"));
                    this.filterBySearch(this.state.filter, true, (e && typeof e === 'string' && e.length > 0 ? e : "all"));
                  }
                }} />
            </div> }

          { showCounter &&
            <p className={classNames.tableItemsCounter}>[{ tableItems.length } / {this.state.allTableItems.length}]</p> } */}

          {this.props.showFillDataButton && 
            <PrimaryButton style={{marginLeft: 20}} text={L("Fill in the form with the data of the selected vehicle")} onClick={() => {
              this.props.onFillDataButtonClick(this._selection.getSelection()[0]);
            }} />
          }
        </LabelContainerComponent>

        <div className={classNames.vehicleListWrapper}>
          <ShimmeredDetailsList
            constrainMode={ConstrainMode.unconstrained}
            columns={this.defaultColumns}
            items={tableItems}
            selectionMode={SelectionMode.single}
            selection={this._selection}
            className={classNames.vehicleList}
            onColumnHeaderClick={this.onColumnClick}
            enableShimmer={(!tableItemsSet && tableItems.length === 0) && 
                (!this.state.filterByVehicleType || this.state.filterByVehicleType.length === 0 || this.state.filterByVehicleType === 'all')}
            selectionPreservedOnEmptyClick={true}
          />
        </div>
    </div>;
  }
}