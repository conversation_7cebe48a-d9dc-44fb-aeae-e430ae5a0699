import * as React from 'react';
import { IColumn, ITextFieldProps, mergeStyleSets, ShimmeredDetailsList, Selection, SelectionMode, SearchBox, FontWeights, ScrollablePane, ScrollbarVisibility, Icon, ConstrainMode } from '@fluentui/react';
import { inject } from 'mobx-react';
import Stores from '../../stores/storeIdentifier';
import { L } from '../../lib/abpUtility';
import { LabelComponent } from './labelComponent';
import { myTheme } from '../../styles/theme';
import { ITableColumn } from './ITableColumn';
import { utilMapToColumn } from '../../utils/tableUtils';
import { LabelContainerComponent } from './labelContainerComponent';
import { isJsonString } from '../../utils/utils';
import { gnInputIconStyles } from '../../styles/gnInputIconStyles';

const classNames = mergeStyleSets({
  hide: {
    display: 'none',
  },
  permissionListWrapper: {
    maxHeight: '350px',
    maxWidth: '100%',
    width: '100%',
    overflow: 'auto',
    margin: '15px 0 0',
    border: `1px solid ${myTheme.palette.themeLighter}`,
    position: 'relative',
    height: 300,
    selectors: {
      // '.ms-DetailsHeader-cell': {
      //   minWidth: '150px',
      // },
      // '.ms-DetailsRow-cell': {
      //   minWidth: '150px',
      // },
      // '.ms-DetailsRow-cellCheck': {
      //   minWidth: 'auto',
      //   width: 'auto',
      // }
    }
  },
  permissionList: {
    selectors: {
      '& .ms-DetailsHeader': {
          paddingTop: '0',
      }
    }
  },
  labelComponentCustom: {
  },
  filterDropdownWrapper: {
    marginLeft: '25px',
    selectors: {
      '& > div': {
        margin: '0 !important'
      }
    }
  },
  tableItemsCounter: {
    margin: "5px 0 0 25px",
    fontWeight: FontWeights.bold,
  },
});

export interface IPermissionListBaseProps extends ITextFieldProps {
  selectedPermission: string[];
  allPermissions: any;
  filterByKey?: string;
  filterValue?: string;
  hideList?: boolean;
  customStyle?: any;
  showFilters?: boolean;
  showCounter?: boolean;
  alignItemsToRight?: boolean;
  customLabel?: string;
  iconName?: string;
  id?: string;
  iconOnClick?: (iconIndex?: number) => void;
  onPermissionSelect: (permission: any) => void;
}

type IPermissionListBaseState = { 
  columnsState: IColumn[],
  tableItems: any[],
  allTableItems: any[],
  tableItemsSet: boolean,
  filter: string,
  initSorted: boolean,
  duringSortOrFilter: boolean,
};

@inject(Stores.EventStore)
export class PermissionListBase extends React.Component<IPermissionListBaseProps, IPermissionListBaseState> {
  private permissionPreselected: boolean = false;
  private initSelection: boolean = true;
  private _selection: Selection;
  private _selectedKeysCopy: any[] = [];

  constructor(props: IPermissionListBaseProps) {
    super(props);

    this.state = {
      ...this.state,
      columnsState: this.mapToColumn(this.getTableColumns()),
      tableItems: [],
      allTableItems: [],
      filter: "",
      tableItemsSet: false,
      initSorted: false,
      duringSortOrFilter: false,
    };

    this._selection = new Selection({
      onSelectionChanged: () => {
        if(!this.initSelection) {
          if(this.state.duringSortOrFilter === false) this._selectedKeysCopy = [...this._selection.getSelection()];
          this.props.onPermissionSelect(this._selection.getSelection());
        } else {
          this.initSelection = false;
        }
        this.forceUpdate();
      },
    });
  }

  componentDidMount() {
    let tableItems = this.props.allPermissions && this.props.allPermissions.length > 0 ? this.props.allPermissions : [];
    tableItems = tableItems.filter(this.filterPermissions, this);

    this.setState({ tableItems: tableItems, allTableItems: tableItems });
  }

  componentDidUpdate() {
    const { allPermissions } = this.props;
    const { tableItems, tableItemsSet, initSorted } = this.state;

    if(!tableItemsSet && ((tableItems.length === 0 && allPermissions && allPermissions.length > 0) || this.state.columnsState.length === 0)) {
      this.setTable();
    }

    if(tableItemsSet && !initSorted) {
      this.setState((prevState) => {
          return ({...prevState, initSorted: true});
      });
      this.sortPermissions({...this.state.columnsState[0]}, true, true);
    }

    if(initSorted && !this.initSelection && !this.permissionPreselected && this.props.selectedPermission && this._selection.getSelectedIndices().length === 0 &&
      (this.props.allPermissions && this.props.allPermissions.length > 0) && this.state.tableItems.length > 0
    ) {
      this.props.selectedPermission.forEach((selectedPermission: string) => {
        if(!!selectedPermission) {
          if(this.state.duringSortOrFilter === false) this._selectedKeysCopy = [...this._selectedKeysCopy, {key: selectedPermission, text: selectedPermission}];
          this._selection.setKeySelected(selectedPermission, true, false);
        }
      });
      this.permissionPreselected = true;
      this.forceUpdate();
    }

    if(this.state.duringSortOrFilter === true && this._selection.getSelectedCount() === 0 && this._selectedKeysCopy.length > 0) {
      this.setState((prevState) => {
        return ({...prevState, duringSortOrFilter: false});
      });

      this._selectedKeysCopy.forEach((selectedPermission: any) => {
        if(selectedPermission && !!selectedPermission.key) {
          this._selection.setKeySelected(selectedPermission.key, true, false);
        }
      });
    }
  }

  private getTableColumns(): ITableColumn[] {
    return [          
      {
        name: L('Name'),
        fieldName: 'text',
        minWidth: 250,
        isSortedDescending: false,
      },
    ];
  }

  private mapToColumn(tableColumns: ITableColumn[]) {
    return utilMapToColumn(tableColumns);
  }

  private getItemFilterString(item: any): string {
    let array: string[] = [];
    this.getTableColumns().forEach((x) => {
      let fieldName = x.fieldName;
      let splittedFieldName: string[] = fieldName.split('.');

      if(splittedFieldName.length > 1) {
        let deeperModelValue = [];
        deeperModelValue[0] = item;
        for(let i = 1; i <= splittedFieldName.length; i++) {
          deeperModelValue[i] = deeperModelValue[i-1][splittedFieldName[i-1]];
        }

        if(!!deeperModelValue[deeperModelValue.length - 1] && deeperModelValue[deeperModelValue.length - 1].length > 0) {
          array.push(deeperModelValue[deeperModelValue.length - 1]);
        }
      } else {
        let property = item[fieldName];
        if(!!property && property.length > 0) {
          array.push(property);
        }
      }
    });

    return array.join();
  }

  private filterPermissions(value: any) {
    const { filterByKey, filterValue } = this.props;

    if(!!filterByKey && !!filterValue) {
      if(Array.isArray(value[filterByKey])) {
        return value[filterByKey].includes(filterValue);
      } else {
        return value[filterByKey] === filterValue;
      }
    }
    return value;
  }

  private filterBySearch(newValue: string, forceSearch?: boolean) {
    this.setState((prevState) => {
      if(newValue !== prevState.filter || forceSearch === true) {
        let tempTableItems = [...this.state.allTableItems];
        if(newValue.length > 0) {
          newValue = newValue.split(" ").join(',');
          tempTableItems = tempTableItems.filter((i) => this.getItemFilterString(i).toString().toLowerCase().indexOf(newValue.toLowerCase()) > -1);  
        }

        const columnFieldName: string = this.state.columnsState[0] ? this.state.columnsState[0].fieldName! : '';
        if(this.state.columnsState[0].isSortedDescending) {
          tempTableItems = tempTableItems.slice(0).sort((a, b) => (a[columnFieldName] < b[columnFieldName]) ? 1 : ((b[columnFieldName] < a[columnFieldName]) ? -1 : 0));
        } else {
          tempTableItems = tempTableItems.slice(0).sort((a, b) => (a[columnFieldName] > b[columnFieldName]) ? 1 : ((b[columnFieldName] > a[columnFieldName]) ? -1 : 0));
        }
        
        return ({...prevState, tableItems: tempTableItems, filter: newValue, duringSortOrFilter: this.state.initSorted ? true : false });
      } else {
        return ({...prevState});
      }
    });
  }

  private sortPermissions(column: IColumn | undefined, changeColumnState: boolean = true, forceAsc?: boolean): void {
    if(column && column.fieldName) {
      let tempTableItems: any[] = [...this.state.tableItems];
      let colIndex: number = -1;

      this.state.columnsState.some((col: any, index: number) => {
        if(col.key === column.key) {
          colIndex = index;
          return true;
        }
        return false;
      });

      if(this.state.columnsState[colIndex].isSortedDescending || forceAsc === true) {
        tempTableItems = tempTableItems.slice(0).sort((a, b) => (a[column.fieldName!] > b[column.fieldName!]) ? 1 : ((b[column.fieldName!] > a[column.fieldName!]) ? -1 : 0));
      } else {
        tempTableItems = tempTableItems.slice(0).sort((a, b) => (a[column.fieldName!] < b[column.fieldName!]) ? 1 : ((b[column.fieldName!] < a[column.fieldName!]) ? -1 : 0));
      }
      
      let newColumnState = [...this.state.columnsState];
      
      if(forceAsc === true) {
        newColumnState[colIndex].isSortedDescending = false;
        this.setState({ columnsState: newColumnState, tableItems: tempTableItems, duringSortOrFilter: this.state.initSorted ? true : false });
      } else if(changeColumnState) {
        newColumnState[colIndex].isSortedDescending = !newColumnState[colIndex].isSortedDescending;
        this.setState({ columnsState: newColumnState, tableItems: tempTableItems, duringSortOrFilter: this.state.initSorted ? true : false });
      } else {
        this.setState({ tableItems: tempTableItems, duringSortOrFilter: this.state.initSorted ? true : false });
      }
    }
  }

  private onColumnClick = (ev?: React.MouseEvent<HTMLElement, MouseEvent> | undefined, newCol?: IColumn | undefined) => {
    // this._selection.setAllSelected(false);
    // this.props.onPermissionSelect('');
    this.sortPermissions(newCol);
  }

  private setTable() {
    if(this.state.tableItems.length === 0) {
      let tableItems = this.props.allPermissions && this.props.allPermissions.length > 0 ? this.props.allPermissions : [];
      tableItems = tableItems.filter(this.filterPermissions, this);

      let tableItemsSet: boolean = false;
      if(tableItems.length > 0) {
        tableItemsSet = true;
      }

      this.setState({ tableItems: tableItems, allTableItems: tableItems, tableItemsSet });
    }
  }

  render() {
    const { customStyle, showFilters, showCounter, hideList, alignItemsToRight } = this.props;
    const { tableItems, tableItemsSet } = this.state;

    let icons: any = <></>;
    
    if(this.props.iconName) {
      if(!isJsonString(this.props.iconName)) {
        icons = <Icon iconName={this.props.iconName} id={`${this.props.id ? this.props.id + '_' : ''}${this.props.iconName}`} className={gnInputIconStyles.icon} onClick={() => this.props.iconOnClick!()} />;
      } else {
        let parsedJsonString: any = JSON.parse(this.props.iconName);
        if(parsedJsonString && Array.isArray(parsedJsonString)) {
          icons = parsedJsonString.map((iconName: string, index: number) => {
            return <Icon iconName={iconName} id={`${this.props.id ? this.props.id + '_' : ''}${this.props.iconName}`} className={gnInputIconStyles.icon} onClick={() => this.props.iconOnClick!(index)} /> 
          });
        }
      }
    }

    return <div className={`${hideList === true && classNames.hide}`} style={customStyle && customStyle}>
      <LabelContainerComponent alignItemsToRight={alignItemsToRight}>
        <LabelComponent label={!!this.props.customLabel ? this.props.customLabel : L('Select permission')} className={classNames.labelComponentCustom} customStyles={showFilters && {width: '200px'}} />
        <SearchBox theme={myTheme} placeholder={ L('Search') }
          styles={{
            root: {
              flex: 1,
              maxWidth: '252px',
              height: '32px', 
              backgroundColor: myTheme.palette.white,
              border: `1px solid ${myTheme.palette.black}`,
              boxSizing: 'border-box',
            },
            field: { borderRadius: '2px' },
          }}
          onChange={ (e: any, newValue: string | undefined) => {
            newValue = typeof newValue === 'undefined' ? "" : newValue;
            this.filterBySearch(newValue, false);
            // this.sortPermissions({...this.state.columnsState[0]}, false);
          }}
          // onSearch={ (newValue: any) => doSomething(newValue) }
        />

        {icons}

        { showFilters &&
          <div className={classNames.filterDropdownWrapper}></div> 
        }

        { showCounter &&
          <p className={classNames.tableItemsCounter}>[{ tableItems.length } / {this.state.allTableItems.length}]</p> }
      </LabelContainerComponent>

      {/* <div className={classNames.permissionListWrapper}> */}
      <ScrollablePane className={classNames.permissionListWrapper} scrollbarVisibility={ScrollbarVisibility.auto} theme={myTheme}>
        <ShimmeredDetailsList
          constrainMode={ConstrainMode.unconstrained}
          columns={this.state.columnsState}
          items={tableItems}
          selectionMode={SelectionMode.multiple}
          selection={this._selection}
          className={classNames.permissionList}
          onColumnHeaderClick={this.onColumnClick}
          enableShimmer={(!tableItemsSet && tableItems.length === 0)}
          selectionPreservedOnEmptyClick={true}
        />
      </ScrollablePane>
      {/* </div> */}
    </div>;
  }
}