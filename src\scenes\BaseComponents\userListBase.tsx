import * as React from 'react';
import { IColumn, ITextFieldProps, mergeStyleSets, ShimmeredDetailsList, Selection, SelectionMode, SearchBox, IDropdownOption, FontWeights, ConstrainMode } from '@fluentui/react';
import { inject } from 'mobx-react';
import Stores from '../../stores/storeIdentifier';
import { L } from '../../lib/abpUtility';
import { LabelComponent } from './labelComponent';
import {additionalTheme, myTheme} from '../../styles/theme';
import { ITableColumn } from './ITableColumn';
import { utilMapToColumn } from '../../utils/tableUtils';
import { LabelContainerComponent } from './labelContainerComponent';
import { DropdownBase } from './dropdownBase';
import { ClientTypeEnum } from '../../services/client/clientTypeEnums';
import { enumToDropdownOptions } from '../../utils/utils';

const classNames = mergeStyleSets({
  hide: {
    display: 'none',
  },
  userListWrapper: {
    maxHeight: '350px',
    maxWidth: '100%',
    width: '100%',
    overflow: 'auto',
    margin: '15px 0 0',
    border: `1px solid ${myTheme.palette.themeLighter}`,
    selectors: {
      // '.ms-DetailsHeader-cell': {
      //   minWidth: '150px',
      // },
      // '.ms-DetailsRow-cell': {
      //   minWidth: '150px',
      // },
      // '.ms-DetailsRow-cellCheck': {
      //   minWidth: 'auto',
      //   width: 'auto',
      // }
    }
  },
  userList: {

    selectors: {
      '& .ms-DetailsHeader': {
          paddingTop: '0',
      }
    }
  },
  labelComponentCustom: {
  },
  filterDropdownWrapper: {
    marginLeft: '25px',
    selectors: {
      '& > div': {
        margin: '0 !important'
      }
    }
  },
  tableItemsCounter: {
    margin: "5px 0 0 25px",
    fontWeight: FontWeights.bold,
  },
});

export interface IUserListBaseProps extends ITextFieldProps {
  selectedUser: string;
  userCrudStore: any;
  filterByKey?: string;
  filterValue?: string;
  hideList?: boolean;
  customStyle?: any;
  showFilters?: boolean;
  showCounter?: boolean;
  alignItemsToRight?: boolean;
  customLabel?: string;
  onUserSelect: (user: any) => void;
}

type IUserListBaseState = { 
  columnsState: any,
  tableItems: any[],
  allTableItems: any[],
  tableItemsSet: boolean,
  filter: string,
  filterByClientType: string,
  filterByClientTypeDropdownOptions: IDropdownOption[],
};

@inject(Stores.EventStore)
export class UserListBase extends React.Component<IUserListBaseProps, IUserListBaseState> {
  private defaultColumns: IColumn[] = [];
  private userPreselected: boolean = false;
  private initSelection: boolean = true;
  private _selection: Selection;

  constructor(props: IUserListBaseProps) {
    super(props);

    this._selection = new Selection({
      onSelectionChanged: () => {
        if(!this.initSelection) {
            this.props.onUserSelect(this._selection.getSelection()[0]);
        } else {
          this.initSelection = false;
          this.forceUpdate();
        }
      },
    });

    this.state = {
      ...this.state,
      columnsState: {},
      tableItems: [],
      allTableItems: [],
      filter: "",
      filterByClientType: "all",
      filterByClientTypeDropdownOptions: [],
      tableItemsSet: false,
    };
  }

  componentDidMount() {
    const mappedColumns = utilMapToColumn(this.getTableColumns());
    let newColumnState = {...this.state.columnsState};

    mappedColumns.forEach((col) => {
      if(!this.state.columnsState[col.key]) {
        newColumnState[col.key] = {
            "isSortedDescending": true
        };
      }
    });

    if(this.props.userCrudStore.dataSet.totalCount === this.props.userCrudStore.dataSet.items.length) {
      let tableItems = this.props.userCrudStore.dataSet && this.props.userCrudStore.dataSet.totalCount > 0 ? this.props.userCrudStore.dataSet.items : [];
      tableItems = tableItems.filter(this.filterUsers, this);
  
      this.setState({ columnsState: newColumnState, tableItems: tableItems, allTableItems: tableItems });
    } else {
      this.setState({ columnsState: newColumnState });
    }
  }

  componentDidUpdate() {
    if(!this.initSelection && !this.userPreselected && this.state.tableItemsSet && this.props.selectedUser && this._selection.getSelectedIndices().length === 0 &&
        (this.props.userCrudStore.dataSet && this.props.userCrudStore.dataSet.totalCount > 0) && this.state.tableItems.length > 0
    ) {
      const foundIndex = this.getIndexByItemId(this.props.selectedUser);

      if(foundIndex >= 0) {
        this._selection.setIndexSelected(foundIndex, true, false);
        this.userPreselected = true;
        this.forceUpdate();
      }
    }
  }

  private getIndexByItemId(itemId: string): number {
    let indexToReturn = -1;
    
    if(this.state.tableItems.length > 0) {
        this.state.tableItems.some((item: any, index: number) => {
          if(item.id === itemId || item.id === parseInt(itemId)) {
              indexToReturn = index;
              return true;
          }
          return false;
        })
    }

    return indexToReturn;
  }

  private getTableColumns(): ITableColumn[] {
    return [          
      {
        name: L('First name'),
        fieldName: 'name',
      },
      {
        name: L('Surname'),
        fieldName: 'surname',
      },
      {
        name: L('E-mail'),
        fieldName: 'emailAddress',
      },
      {
        name: L('Username'),
        fieldName: 'userName',
      },
      {
        name: L('Is active'),
        fieldName: 'isActive',
        onRender: (item: any): any => {
          return item.isActive ? (
            <span style={{ color: additionalTheme.white, backgroundColor: myTheme.palette.themePrimary, padding: '2px 5px', borderRadius: '2px' }}>{L('Yes')}</span>
          ) : (
            <span>{L('No')}</span>
          );
        }
      },
    ];
  }

  private mapToColumn(tableColumns: ITableColumn[]) {
    return utilMapToColumn(tableColumns)
  }

  private getItemFilterString(item: any): string {
    let array: string[] = [];
    this.getTableColumns().forEach((x) => {
      let fieldName = x.fieldName;
      let splittedFieldName: string[] = fieldName.split('.');

      if(splittedFieldName.length > 1) {
        let deeperModelValue = [];
        deeperModelValue[0] = item;
        for(let i = 1; i <= splittedFieldName.length; i++) {
          deeperModelValue[i] = deeperModelValue[i-1][splittedFieldName[i-1]];
        }

        if(!!deeperModelValue[deeperModelValue.length - 1] && deeperModelValue[deeperModelValue.length - 1].length > 0) {
          array.push(deeperModelValue[deeperModelValue.length - 1]);
        }
      } else {
        let property = item[fieldName];
        if(!!property && property.length > 0) {
          array.push(property);
        }
      }
    });

    return array.join();
  }

  private filterUsers(value: any) {
    const { filterByKey, filterValue } = this.props;

    if(!!filterByKey && !!filterValue) {
      if(Array.isArray(value[filterByKey])) {
        return value[filterByKey].includes(filterValue) && !value.Deleted;
      } else {
        return value[filterByKey] === filterValue && !value.Deleted;
      }
    }
    return !value.Deleted;
  }

  private filterByClientType(clientType: string) {
    this.setState((prevState) => {
      if(clientType !== prevState.filterByClientType) {
        let tempTableItems = [...this.state.allTableItems];
        if(clientType.length > 0) {
          tempTableItems = tempTableItems.filter((x) => x.clientType === clientType);
        }

        return ({...prevState, tableItems: tempTableItems, filterByClientType: clientType});
      } else {
        return ({...prevState});
      }
    });
  }

  private filterBySearch(newValue: string, forceSearch?: boolean, newClientType?: string) {
    this.setState((prevState) => {
      if(newValue !== prevState.filter || forceSearch === true) {
        let tempTableItems = [...this.state.allTableItems];
        if(newValue.length > 0) {
          newValue = newValue.split(" ").join(',');
          tempTableItems = tempTableItems.filter((i) => this.getItemFilterString(i).toString().toLowerCase().indexOf(newValue.toLowerCase()) > -1);
        }

        let finalTableItems: any[] = [];
        if(!!newClientType && newClientType !== 'all') {
          tempTableItems.forEach((tableItem: any) => {
            if(tableItem.clientType === newClientType) {
              finalTableItems.push(tableItem);
            }
          });
        } else {
          finalTableItems = tempTableItems;
        }

        return ({...prevState, tableItems: finalTableItems, filter: newValue});
      } else {
        return ({...prevState});
      }
    });
  }

  private sortUsers(column: IColumn | undefined): void {
    if(column && column.name) {
      let tempTableItems = [...this.state.tableItems];

      if(this.state.columnsState[column.key].isSortedDescending) {
        tempTableItems = tempTableItems.slice(0).sort((a, b) => (a[column.name] < b[column.name]) ? 1 : ((b[column.name] < a[column.name]) ? -1 : 0));
      } else {
        tempTableItems = tempTableItems.slice(0).sort((a, b) => (a[column.name] > b[column.name]) ? 1 : ((b[column.name] > a[column.name]) ? -1 : 0));
      }
      
      let newColumnState = {...this.state.columnsState};
      newColumnState[column.key].isSortedDescending = !newColumnState[column.key].isSortedDescending;
      this.setState({ columnsState: newColumnState, tableItems: tempTableItems });
    }
  }

  private onColumnClick = (ev?: React.MouseEvent<HTMLElement, MouseEvent> | undefined, newCol?: IColumn | undefined) => {
    this._selection.setAllSelected(false);
    this.props.onUserSelect('');
    this.sortUsers(newCol);
  }

  private setTable() {
    let tableColumns = this.getTableColumns();
    this.defaultColumns = this.mapToColumn(tableColumns);

    if(this.state.tableItems.length === 0 && this.props.userCrudStore.dataSet.totalCount === this.props.userCrudStore.dataSet.items.length) {
      let tableItems = this.props.userCrudStore.dataSet && this.props.userCrudStore.dataSet.totalCount > 0 ? this.props.userCrudStore.dataSet.items : [];
      tableItems = tableItems.filter(this.filterUsers, this);

      let tableItemsSet: boolean = false;
      if(tableItems.length > 0) {
        tableItemsSet = true;
      }

      this.setState({ tableItems: tableItems, allTableItems: tableItems, tableItemsSet });
    }
  }

  private setDropdownOptions() {
    let tempOptions: IDropdownOption[] = [
      { key: 'all', text: L('All2') },
    ];

    let clientTypeEnumOptions: IDropdownOption[] = enumToDropdownOptions(ClientTypeEnum, true, true, "string");

    if(tempOptions.length > this.state.filterByClientTypeDropdownOptions.length) {
      this.setState({ filterByClientTypeDropdownOptions: [...tempOptions, ...clientTypeEnumOptions] });
    }
  }

  render() {
    const { userCrudStore, customStyle, showFilters, showCounter, hideList, alignItemsToRight } = this.props;
    const { filterByClientTypeDropdownOptions, tableItems, filterByClientType, tableItemsSet } = this.state;

    if(!tableItemsSet && ((tableItems.length === 0 && userCrudStore.dataSet && userCrudStore.dataSet.totalCount > 0) || this.defaultColumns.length === 0)) {
      if(!this.state.filterByClientType || this.state.filterByClientType.length === 0 || this.state.filterByClientType === 'all') {
        this.setTable();
      }
    }

    if(!filterByClientTypeDropdownOptions || filterByClientTypeDropdownOptions.length <= 1) {
      this.setDropdownOptions();
    }

    return <div className={`${hideList === true && classNames.hide}`} style={customStyle && customStyle}>
        <LabelContainerComponent alignItemsToRight={alignItemsToRight}>
          <LabelComponent label={!!this.props.customLabel ? this.props.customLabel : L('Select user')} className={classNames.labelComponentCustom} customStyles={showFilters && {width: '200px'}} />
          <SearchBox
              theme={myTheme}
              styles={{
                root: {
                  flex: 1,
                  maxWidth: '252px',
                  height: '32px', 
                  backgroundColor: myTheme.palette.white,
                  border: `1px solid ${myTheme.palette.black}`,
                  boxSizing: 'border-box',
                },
                field: { borderRadius: '2px' },
              }}
              placeholder={ L('Search') }
              onChange={ (e: any, newValue: string | undefined) => {
                newValue = typeof newValue === 'undefined' ? "" : newValue;
                this.filterBySearch(newValue, false, filterByClientType);
              }}
              // onSearch={ (newValue: any) => doSomething(newValue) }
            />

            { showFilters &&
              <div className={classNames.filterDropdownWrapper}>
                <DropdownBase key={'filterByClientTypeDropdown'} required={false} label={L('Filter by client type')} options={filterByClientTypeDropdownOptions}
                  value={filterByClientType} disabled={false} isDataLoaded={true} customDropdownWidth="250px" 
                  customLabelStyles={{minWidth: '200px', width: '200px'}}
                  onChange={(e: string | number | undefined) => {
                    if(e !== filterByClientType) {
                      this.filterByClientType((e && typeof e === 'string' && e.length > 0 ? e : "all"));
                      this.filterBySearch(this.state.filter, true, (e && typeof e === 'string' && e.length > 0 ? e : "all"));
                    }
                  }} />
              </div> }

            { showCounter &&
              <p className={classNames.tableItemsCounter}>[{ tableItems.length } / {this.state.allTableItems.length}]</p> }
        </LabelContainerComponent>

        <div className={classNames.userListWrapper}>
          <ShimmeredDetailsList
            constrainMode={ConstrainMode.unconstrained}
            columns={this.defaultColumns}
            items={tableItems}
            selectionMode={SelectionMode.single}
            selection={this._selection}
            className={classNames.userList}
            onColumnHeaderClick={this.onColumnClick}
            enableShimmer={(!tableItemsSet && tableItems.length === 0) && 
                (!this.state.filterByClientType || this.state.filterByClientType.length === 0 || this.state.filterByClientType === 'all')}
            selectionPreservedOnEmptyClick={true}
          />
        </div>
    </div>;
  }
}