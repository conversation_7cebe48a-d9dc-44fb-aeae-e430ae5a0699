import { Pivot, PivotItem, Selection, SelectionMode } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { Controls } from '../../BaseComponents/controls';
import { ContentViewModelProperty } from '../../BaseComponents/contentViewBase';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import {additionalTheme, myTheme} from '../../../styles/theme';
import { LoyaltyPointsHistoryDto } from '../../../services/loyaltyPointsHistory/loyaltyPointsHistoryDto';
import { defaultLoyaltyPointsHistory } from '../../../stores/loyaltyPointsHistoryStore';
import Stores from '../../../stores/storeIdentifier';
import { inject } from 'mobx-react';
import { fluentTableClassNames } from '../../../styles/fluentTableStyles';
import { CustomerFluentListBase } from '../../BaseComponents/customerFluentListBase';

@inject(Stores.ClientStore)
export class LoyaltyPointsHistoryContentView extends GenericContentView {
    private loyaltyPointsHistory: LoyaltyPointsHistoryDto = defaultLoyaltyPointsHistory;
    private selectClientSearchText: string = "";
    private _clientListSelection: Selection = new Selection({
        onSelectionChanged: () => {
            const selectedClient: any = this._clientListSelection.getSelection();
            if(Array.isArray(selectedClient) && selectedClient.length > 0 && !!selectedClient[0].id) {
                this.loyaltyPointsHistory.CustomerId = selectedClient[0].id.toString();
                this.loyaltyPointsHistory.CustomerName = !!selectedClient[0].fullName ? selectedClient[0].fullName : selectedClient[0].user.fullName;
                this._clientListSelection.setAllSelected(false);
            }
            this.forceUpdate();
        },
        selectionMode: SelectionMode.single
    });

    async componentDidMount() {
        this.checkIfDataIsLoaded("loyaltyPointsHistory");
    }

    // private onCustomerSelect(customer: any) {
    //     let newModel = this.state.model;

    //     if(customer && typeof customer !== 'undefined') {
    //         newModel.value["CustomerId"] = customer.id.toString();
    //     } else {
    //         newModel.value["CustomerId"] = "";
    //     }
    //     this.setState({ model: newModel });
    // }

    private validateMinimumDataloyaltyPointsHistory(loyaltyPointsHistory: LoyaltyPointsHistoryDto) {
        if((loyaltyPointsHistory.CustomerId && 
            ((typeof loyaltyPointsHistory.CustomerId === 'number' && loyaltyPointsHistory.CustomerId > 0) ||
            (typeof loyaltyPointsHistory.CustomerId === 'string' && loyaltyPointsHistory.CustomerId.length > 0))) && 
            (loyaltyPointsHistory.Points && Number(loyaltyPointsHistory.Points) > 0)
        ) {
            this.props.toggleConfirm!(true);
        } else {
            this.props.toggleConfirm!(false);
        }
    }

    renderContent() {
        this.loyaltyPointsHistory = this.props.payload.model ? this.props.payload.model : this.props.payload;
        this.validateMinimumDataloyaltyPointsHistory(this.loyaltyPointsHistory);

        const pivotStyles = {
            root: {
                marginLeft: '-8px'
            },
            linkIsSelected: {
                color: myTheme.palette.red,
                selectors: {
                    ':before': {
                        height: '5px',
                        backgroundColor: additionalTheme.darkerRed
                    }
                }
            }
        };

        return <Pivot theme={myTheme} styles={pivotStyles}>
            <PivotItem headerText={L('General')} key={'General'}>
                <div className={fluentTableClassNames.contentContainer}>
                    <CustomerFluentListBase
                        searchText={this.selectClientSearchText}
                        items={this.props.clientStore?.dataSet && this.props.clientStore?.dataSet.items ? this.props.clientStore?.dataSet.items : []}
                        store={this.props.clientStore!}
                        history={this.props.history}
                        scrollablePanelMarginTop={70}
                        customData={{
                            selectedClient: this.loyaltyPointsHistory.CustomerId && this.loyaltyPointsHistory.CustomerName ? `[${this.loyaltyPointsHistory.CustomerId}] ${this.loyaltyPointsHistory.CustomerName}` : undefined,
                        }}
                        customOnSearchTextChanged={(text: string) => { this.selectClientSearchText = text; this.forceUpdate(); }}
                        customSelection={this._clientListSelection}
                        customOnSelectionChanged={(selection: any) => {
                            if(typeof selection === 'string' && selection === 'deleteClient') {
                                this.loyaltyPointsHistory.CustomerId = defaultLoyaltyPointsHistory.CustomerId;
                                this.loyaltyPointsHistory.CustomerName = "";
                                this.forceUpdate();
                            }
                        }}
                    />
                </div>

                {this.renderElement(new ContentViewModelProperty('Points', "Points", Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, textType: 'number'}), [], {'Points': this.loyaltyPointsHistory.Points})}
                {this.renderElement(new ContentViewModelProperty('Message', "Message", Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, rows: 5}), [], {'Message': this.loyaltyPointsHistory.Message})}
            </PivotItem>
        </Pivot>
    }
}