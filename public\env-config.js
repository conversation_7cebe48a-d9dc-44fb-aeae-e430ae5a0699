window._env_ = { "GENERATE_SOURCEMAP": "false", 
"REACT_APP_APP_BASE_URL": "https://top-api.astroid.pl/", 
// "REACT_APP_APP_BASE_URL": "https://top-api.a-soft.pl/",
"REACT_APP_REMOTE_SERVICE_BASE_URL": "https://top-api.astroid.pl/",
// "REACT_APP_REMOTE_SERVICE_BASE_URL": "https://top-api.a-soft.pl/",
"REACT_APP_GRAND_NODE_BASE_URL": "https://top-gn-dev.azurewebsites.net/",
"AUTHORITY": "https://toptest3.b2clogin.com/toptest3.onmicrosoft.com/B2C_1_SignInSignUp_TOP_AppService",
// "AUTHORITY": "https://toptest3.b2clogin.com/toptest3.onmicrosoft.com/B2C_1_SignInSignUp_TOP",
"CLIENTID": "d98326e2-7b89-4972-a84b-213bdace833a",
"SCOPE": "https://toptest3.onmicrosoft.com/d98326e2-7b89-4972-a84b-213bdace833a/top-aswagger",
"ALLOWED_CONTENT": "ALL", // OPTION_1
"DATABASE": "DEV", // or DEMO or PROD
"LOGO_PATH": "https://toptmp.blob.core.windows.net/public-files/images/logo.png",
"LOGO_ALT": "TOP-Ubezpieczenia",
"INSTA_LINK": "https://www.instagram.com/topubezpieczenia/",
"FB_LINK": "https://www.facebook.com/topubezpieczenia/",
"LEFT_HEADER_TEXT": "",
"RIGHT_HEADER_TEXT": "",
"DEFAULT_SEARCH_BAR_DELAY_IN_MS": 500,
"DEFAULT_INPUTS_DELAY_IN_MS": 400,
"DISABLE_CHAT": true,
"IS_CONFIG_FOR_AG": false, // OLO_CLEVER_PROD - false, TOP_PROD - false, AG - true
"IS_CONFIG_FOR_PRODUCTION": false, // OLO_CLEVER_PROD - false, TOP_PROD - true, AG - true
"SHOW_FILTER_BY_ROLE_ON_USER_LIST": true, 
"SHOW_ARCHIVED_TAB_ON_USER_LIST": false,
"IS_CONFIG_FOR_UAT": false,
"FAVICON_PATH": "", }
window.version = "0.1.4"

window.azureb2cRedirect = "http://localhost:3000/user/login"